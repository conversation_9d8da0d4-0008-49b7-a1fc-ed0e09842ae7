"""
Unit tests for the stock optimizer module.
"""

import unittest
import numpy as np
import pandas as pd
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.stock_optimizer import StockOptimizer
from src.data_provider import SyntheticDataProvider, create_sample_data
from src.qubo_formulation import QUBOFormulation


class TestStockOptimizer(unittest.TestCase):
    """Test cases for StockOptimizer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.data_provider = SyntheticDataProvider(n_stocks=10, seed=42)
        self.optimizer = StockOptimizer(
            data_provider=self.data_provider,
            lambda_correlation=1.0,
            lambda_balance=1.0,
            lambda_constraints=10.0,
            solver_type='simulated_annealing'
        )
        
        # Create test data
        self.returns, self.sector_info = create_sample_data(n_stocks=10, n_days=100)
        self.universe = list(self.returns.columns)
        
    def test_initialization(self):
        """Test optimizer initialization."""
        self.assertIsInstance(self.optimizer.qubo_formulation, QUBOFormulation)
        self.assertEqual(self.optimizer.solver_type, 'simulated_annealing')
        self.assertIsNone(self.optimizer.last_solution)
        self.assertIsNone(self.optimizer.last_objective)
        
    def test_select_stocks_basic(self):
        """Test basic stock selection functionality."""
        result = self.optimizer.select_stocks(
            universe=self.universe,
            target_stocks=3,
            lookback_days=50,
            num_reads=100
        )
        
        # Check result structure
        self.assertIn('selected_stocks', result)
        self.assertIn('solution_vector', result)
        self.assertIn('energy', result)
        self.assertIn('objective_components', result)
        self.assertIn('validation', result)
        self.assertIn('solver_info', result)
        
        # Check constraints
        self.assertEqual(len(result['selected_stocks']), 3)
        self.assertTrue(result['validation']['num_stocks_ok'])
        
        # Check solution vector
        self.assertEqual(len(result['solution_vector']), len(self.universe))
        self.assertEqual(sum(result['solution_vector']), 3)
        
    def test_select_stocks_with_sectors(self):
        """Test stock selection with sector constraints."""
        result = self.optimizer.select_stocks(
            universe=self.universe,
            target_stocks=4,
            lookback_days=50,
            sector_info=self.sector_info,
            max_sector_weight=0.5,
            num_reads=100
        )
        
        self.assertEqual(len(result['selected_stocks']), 4)
        self.assertTrue(result['validation']['sector_constraints_ok'])
        
    def test_select_stocks_edge_cases(self):
        """Test edge cases for stock selection."""
        # Test with target_stocks = 1
        result = self.optimizer.select_stocks(
            universe=self.universe[:5],
            target_stocks=1,
            lookback_days=30,
            num_reads=50
        )
        self.assertEqual(len(result['selected_stocks']), 1)
        
        # Test with all stocks
        result = self.optimizer.select_stocks(
            universe=self.universe[:5],
            target_stocks=5,
            lookback_days=30,
            num_reads=50
        )
        self.assertEqual(len(result['selected_stocks']), 5)
        
    def test_invalid_inputs(self):
        """Test handling of invalid inputs."""
        # Test with target_stocks > universe size
        with self.assertRaises(ValueError):
            self.optimizer.select_stocks(
                universe=self.universe[:3],
                target_stocks=5,
                lookback_days=30
            )
        
        # Test with empty universe
        with self.assertRaises(ValueError):
            self.optimizer.select_stocks(
                universe=[],
                target_stocks=3,
                lookback_days=30
            )
            
    def test_rebalance_portfolio(self):
        """Test portfolio rebalancing functionality."""
        current_portfolio = self.universe[:3]
        
        result = self.optimizer.rebalance_portfolio(
            current_portfolio=current_portfolio,
            universe=self.universe,
            target_stocks=3,
            lookback_days=50,
            min_turnover=0.0,
            max_turnover=1.0,
            num_reads=100
        )
        
        # Check result structure
        self.assertIn('new_portfolio', result)
        self.assertIn('stocks_to_buy', result)
        self.assertIn('stocks_to_sell', result)
        self.assertIn('turnover', result)
        self.assertIn('optimal_result', result)
        
        # Check constraints
        self.assertEqual(len(result['new_portfolio']), 3)
        self.assertGreaterEqual(result['turnover'], 0.0)
        self.assertLessEqual(result['turnover'], 1.0)
        
    def test_different_solver_types(self):
        """Test different solver types."""
        solver_types = ['simulated_annealing', 'sqa']
        
        for solver_type in solver_types:
            optimizer = StockOptimizer(
                data_provider=self.data_provider,
                solver_type=solver_type
            )
            
            result = optimizer.select_stocks(
                universe=self.universe[:5],
                target_stocks=2,
                lookback_days=30,
                num_reads=50
            )
            
            self.assertEqual(len(result['selected_stocks']), 2)
            self.assertEqual(result['solver_info']['solver_type'], solver_type)
            
    def test_objective_components(self):
        """Test objective function components calculation."""
        result = self.optimizer.select_stocks(
            universe=self.universe,
            target_stocks=3,
            lookback_days=50,
            num_reads=100
        )
        
        obj_comp = result['objective_components']
        
        # Check all components are present
        required_keys = [
            'total_objective', 'profitability', 'correlation_penalty',
            'balance_penalty', 'expected_return', 'avg_correlation'
        ]
        
        for key in required_keys:
            self.assertIn(key, obj_comp)
            self.assertIsInstance(obj_comp[key], (int, float))
            
    def test_solver_parameters(self):
        """Test custom solver parameters."""
        result = self.optimizer.select_stocks(
            universe=self.universe[:5],
            target_stocks=2,
            lookback_days=30,
            num_reads=200,
            num_sweeps=500,
            beta_min=0.5,
            beta_max=5.0
        )
        
        solver_info = result['solver_info']
        params = solver_info['parameters']
        
        self.assertEqual(params['num_reads'], 200)
        self.assertEqual(params['num_sweeps'], 500)
        self.assertEqual(params['beta_min'], 0.5)
        self.assertEqual(params['beta_max'], 5.0)


class TestQUBOFormulation(unittest.TestCase):
    """Test cases for QUBO formulation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.qubo = QUBOFormulation(
            lambda_correlation=1.0,
            lambda_balance=1.0,
            lambda_constraints=10.0
        )
        
        # Create simple test data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        data = np.random.randn(50, 5) * 0.02  # 2% daily volatility
        self.returns = pd.DataFrame(data, index=dates, columns=['A', 'B', 'C', 'D', 'E'])
        
    def test_qubo_matrix_creation(self):
        """Test QUBO matrix creation."""
        Q, linear = self.qubo.formulate_qubo(
            returns=self.returns,
            target_stocks=3
        )
        
        # Check dimensions
        n_stocks = len(self.returns.columns)
        self.assertEqual(Q.shape, (n_stocks, n_stocks))
        self.assertEqual(len(linear), n_stocks)
        
        # Check symmetry (Q should be upper triangular in our formulation)
        for i in range(n_stocks):
            for j in range(i):
                self.assertEqual(Q[i, j], 0)  # Lower triangle should be zero
                
    def test_solution_validation(self):
        """Test solution validation."""
        solution = {'A': 1, 'B': 1, 'C': 1, 'D': 0, 'E': 0}
        
        validation = self.qubo.validate_solution(
            solution=solution,
            target_stocks=3
        )
        
        self.assertTrue(validation['num_stocks_ok'])
        self.assertEqual(validation['selected_count'], 3)
        
    def test_objective_calculation(self):
        """Test objective function calculation."""
        solution = {'A': 1, 'B': 1, 'C': 1, 'D': 0, 'E': 0}
        
        obj_value = self.qubo.calculate_objective_value(
            solution=solution,
            returns=self.returns
        )
        
        required_keys = [
            'total_objective', 'profitability', 'correlation_penalty',
            'balance_penalty', 'expected_return', 'avg_correlation'
        ]
        
        for key in required_keys:
            self.assertIn(key, obj_value)
            
    def test_sector_constraints(self):
        """Test sector constraint handling."""
        sector_info = {'A': 'Tech', 'B': 'Tech', 'C': 'Finance', 'D': 'Finance', 'E': 'Energy'}
        
        Q, linear = self.qubo.formulate_qubo(
            returns=self.returns,
            target_stocks=3,
            sector_info=sector_info,
            max_sector_weight=0.4
        )
        
        # Should have additional penalty terms for sector constraints
        self.assertIsInstance(Q, np.ndarray)
        self.assertIsInstance(linear, np.ndarray)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
