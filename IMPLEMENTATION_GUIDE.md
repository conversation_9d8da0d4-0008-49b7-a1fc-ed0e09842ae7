# Implementation Guide: Real-Time Trading System with OpenJij

This document provides a comprehensive guide to the implementation of the stock selection system based on the paper "Real-Time Trading System Based on Selections of Potentially Profitable, Uncorrelated, and Balanced Stocks by NP-Hard Combinatorial Optimization" using OpenJij instead of the SBM solver.

## Overview

The system implements a quantum-inspired approach to portfolio optimization by formulating stock selection as a QUBO (Quadratic Unconstrained Binary Optimization) problem. The key innovation is using OpenJij, a Python library for quantum annealing simulation, to solve the optimization problem efficiently.

## Mathematical Formulation

### QUBO Objective Function

The stock selection problem is formulated as:

```
minimize: H = H_profit + λ₁H_correlation + λ₂H_balance + λ₃H_constraints
```

Where:
- **H_profit**: Profitability term (negative expected returns to maximize profit)
- **H_correlation**: Correlation penalty to encourage diversification
- **H_balance**: Portfolio balance term for equal weighting
- **H_constraints**: Hard constraints (number of stocks, sector limits)
- **λ₁, λ₂, λ₃**: Penalty coefficients

### Detailed Terms

1. **Profitability Term**:
   ```
   H_profit = -Σᵢ rᵢxᵢ
   ```
   Where rᵢ is the expected return of stock i, xᵢ is the binary selection variable.

2. **Correlation Penalty**:
   ```
   H_correlation = λ₁ Σᵢⱼ ρᵢⱼxᵢxⱼ
   ```
   Where ρᵢⱼ is the correlation between stocks i and j.

3. **Balance Term**:
   ```
   H_balance = λ₂ Σᵢⱼ xᵢxⱼ - (λ₂/n)Σᵢ xᵢ
   ```
   Encourages equal portfolio weights.

4. **Constraint Term**:
   ```
   H_constraints = λ₃(Σᵢ xᵢ - k)²
   ```
   Where k is the target number of stocks.

## Key Components

### 1. QUBO Formulation (`src/qubo_formulation.py`)

The `QUBOFormulation` class converts the stock selection problem into a QUBO matrix:

```python
Q, linear = qubo_formulation.formulate_qubo(
    returns=returns_data,
    target_stocks=5,
    sector_info=sector_mapping,
    max_sector_weight=0.4
)
```

**Key Features**:
- Automatic QUBO matrix construction
- Sector diversification constraints
- Solution validation
- Objective function decomposition

### 2. Stock Optimizer (`src/stock_optimizer.py`)

The `StockOptimizer` class uses OpenJij to solve the QUBO problem:

```python
optimizer = StockOptimizer(
    data_provider=data_provider,
    lambda_correlation=1.5,
    lambda_balance=1.0,
    lambda_constraints=10.0,
    solver_type='simulated_annealing'
)

result = optimizer.select_stocks(
    universe=stock_universe,
    target_stocks=8,
    lookback_days=252,
    num_reads=1000
)
```

**Supported Solvers**:
- Simulated Annealing (`simulated_annealing`)
- Simulated Quantum Annealing (`sqa`)
- Custom solver configurations

### 3. Data Provider (`src/data_provider.py`)

Multiple data sources supported:

```python
# Yahoo Finance (real data)
data_provider = YahooDataProvider()

# Synthetic data (for testing)
data_provider = SyntheticDataProvider(n_stocks=20, seed=42)

# Quick sample data generation
returns, sector_info = create_sample_data(n_stocks=15, n_days=252)
```

### 4. Risk Management (`src/risk_manager.py`)

Comprehensive risk assessment and management:

```python
risk_manager = RiskManager(
    max_portfolio_volatility=0.20,
    max_individual_weight=0.15,
    max_sector_weight=0.40,
    max_drawdown_threshold=0.10
)

risk_assessment = risk_manager.assess_portfolio_risk(
    portfolio=selected_stocks,
    prices=price_data,
    weights=portfolio_weights,
    sector_info=sector_mapping
)
```

### 5. Real-Time Trading System (`src/trading_system.py`)

Automated portfolio management:

```python
trading_system = RealTimeTradingSystem(
    data_provider=data_provider,
    optimizer=optimizer,
    risk_manager=risk_manager,
    universe=stock_universe,
    target_stocks=10,
    rebalance_frequency='daily',
    max_turnover=0.3
)

trading_system.start(initial_portfolio=current_holdings)
```

## OpenJij Integration

### Why OpenJij?

OpenJij provides several advantages over traditional optimization methods:

1. **Quantum-Inspired Algorithms**: Simulated annealing and quantum annealing simulation
2. **Scalability**: Handles large QUBO problems efficiently
3. **Flexibility**: Multiple solver types and parameter tuning
4. **Performance**: Fast convergence for combinatorial optimization

### Solver Configuration

```python
# Simulated Annealing
sampler = oj.SASampler()
response = sampler.sample_qubo(qubo_matrix, 
                              num_reads=1000,
                              num_sweeps=2000,
                              beta_min=0.1,
                              beta_max=10.0)

# Simulated Quantum Annealing
sampler = oj.SQASampler()
response = sampler.sample_qubo(qubo_matrix,
                              num_reads=1000,
                              num_sweeps=2000,
                              beta=10.0,
                              gamma=1.0)
```

## Usage Examples

### Basic Stock Selection

```python
from src.data_provider import YahooDataProvider
from src.stock_optimizer import StockOptimizer

# Setup
data_provider = YahooDataProvider()
optimizer = StockOptimizer(data_provider)

# Select stocks
result = optimizer.select_stocks(
    universe=['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'],
    target_stocks=3,
    lookback_days=252
)

print(f"Selected stocks: {result['selected_stocks']}")
print(f"Expected return: {result['objective_components']['expected_return']:.4f}")
print(f"Average correlation: {result['objective_components']['avg_correlation']:.4f}")
```

### Portfolio Rebalancing

```python
# Rebalance existing portfolio
rebalance_result = optimizer.rebalance_portfolio(
    current_portfolio=['AAPL', 'GOOGL', 'MSFT'],
    universe=extended_universe,
    target_stocks=5,
    max_turnover=0.4
)

print(f"Stocks to buy: {rebalance_result['stocks_to_buy']}")
print(f"Stocks to sell: {rebalance_result['stocks_to_sell']}")
print(f"Portfolio turnover: {rebalance_result['turnover']:.2%}")
```

### Real-Time Trading

```python
# Setup callbacks
def on_rebalance(trade_record):
    print(f"Portfolio rebalanced: {trade_record['new_portfolio']}")

def on_trade(trade):
    print(f"Trade executed: {trade['action']} {trade['symbol']}")

# Start trading system
trading_system.set_callbacks(on_rebalance=on_rebalance, on_trade=on_trade)
trading_system.start()
```

## Performance Optimization

### Parameter Tuning

1. **Correlation Weight (λ₁)**:
   - Higher values: Lower correlation, more diversified portfolios
   - Lower values: Focus more on returns and balance

2. **Balance Weight (λ₂)**:
   - Higher values: More equal-weighted portfolios
   - Lower values: Allow concentration in best stocks

3. **Constraint Weight (λ₃)**:
   - Should be high enough to enforce hard constraints
   - Typical range: 10-100

### Solver Parameters

1. **Number of Reads**: More reads improve solution quality but increase computation time
2. **Number of Sweeps**: Higher values allow better exploration of solution space
3. **Temperature Schedule**: Beta parameters control the annealing schedule

## Testing and Validation

### Unit Tests

```bash
python -m pytest tests/ -v
```

### Basic Example

```bash
python examples/basic_optimization.py
```

### Real-Time Demo

```bash
python examples/real_time_trading.py
```

### Simple Test (No Dependencies)

```bash
python simple_test.py
```

## Installation

### Full Installation

```bash
pip install -r requirements.txt
```

### Minimal Installation

```bash
pip install openjij numpy pandas yfinance scikit-learn matplotlib
```

## Comparison with Original Paper

| Aspect | Original Paper | This Implementation |
|--------|----------------|-------------------|
| Solver | SBM (Simulated Bifurcation Machine) | OpenJij (Quantum Annealing Simulation) |
| Language | Not specified | Python |
| Data Source | Real market data | Yahoo Finance + Synthetic |
| Real-time | Yes | Yes (simulated) |
| Risk Management | Basic | Comprehensive |
| Visualization | Limited | Full analysis plots |

## Future Enhancements

1. **Advanced Solvers**: Integration with quantum hardware (D-Wave, IBM Quantum)
2. **Machine Learning**: Predictive models for expected returns
3. **Alternative Data**: Sentiment analysis, news feeds, social media
4. **High-Frequency**: Microsecond-level optimization
5. **Multi-Asset**: Extension to bonds, commodities, cryptocurrencies

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **Data Issues**: Check internet connection for Yahoo Finance data
3. **Solver Convergence**: Increase num_reads or adjust temperature parameters
4. **Memory Issues**: Reduce universe size or use chunked processing

### Performance Tips

1. Use synthetic data for testing and development
2. Start with small universes (10-20 stocks) for parameter tuning
3. Cache data to avoid repeated API calls
4. Use parallel processing for multiple optimizations

## References

1. Tatsumura, K., et al. (2023). "Real-Time Trading System Based on Selections of Potentially Profitable, Uncorrelated, and Balanced Stocks by NP-Hard Combinatorial Optimization." IEEE Access.
2. OpenJij Documentation: https://openjij.github.io/OpenJij/
3. QUBO Formulations: Lucas, A. (2014). "Ising formulations of many NP problems." Frontiers in Physics.
