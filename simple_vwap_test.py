#!/usr/bin/env python3
"""
Simple VWAP Calculator Test

This demonstrates VWAP calculations without external dependencies,
showing the core mathematical concepts and implementations.
"""

import math
import random
from datetime import datetime, timedelta


class SimpleVWAPCalculator:
    """
    Simple VWAP calculator without external dependencies.
    
    VWAP (Volume Weighted Average Price) is calculated as:
    VWAP = Σ(Price × Volume) / Σ(Volume)
    
    Where Price is typically the typical price: (High + Low + Close) / 3
    """
    
    def __init__(self):
        """Initialize the calculator."""
        pass
    
    def calculate_typical_price(self, high, low, close):
        """Calculate typical price: (H + L + C) / 3"""
        return (high + low + close) / 3
    
    def calculate_vwap(self, price_volume_data):
        """
        Calculate VWAP from price and volume data.
        
        Args:
            price_volume_data: List of tuples (price, volume)
            
        Returns:
            VWAP value
        """
        if not price_volume_data:
            return 0.0
        
        total_pv = sum(price * volume for price, volume in price_volume_data)
        total_volume = sum(volume for _, volume in price_volume_data)
        
        if total_volume == 0:
            return 0.0
        
        return total_pv / total_volume
    
    def calculate_cumulative_vwap(self, ohlcv_data):
        """
        Calculate cumulative VWAP from OHLCV data.
        
        Args:
            ohlcv_data: List of dictionaries with keys: 'open', 'high', 'low', 'close', 'volume'
            
        Returns:
            List of VWAP values (cumulative)
        """
        if not ohlcv_data:
            return []
        
        vwap_values = []
        cumulative_pv = 0.0
        cumulative_volume = 0.0
        
        for bar in ohlcv_data:
            # Calculate typical price
            typical_price = self.calculate_typical_price(
                bar['high'], bar['low'], bar['close']
            )
            
            # Update cumulative values
            pv = typical_price * bar['volume']
            cumulative_pv += pv
            cumulative_volume += bar['volume']
            
            # Calculate VWAP
            if cumulative_volume > 0:
                vwap = cumulative_pv / cumulative_volume
            else:
                vwap = 0.0
            
            vwap_values.append(vwap)
        
        return vwap_values
    
    def calculate_rolling_vwap(self, ohlcv_data, window):
        """
        Calculate rolling VWAP with specified window.
        
        Args:
            ohlcv_data: List of OHLCV dictionaries
            window: Rolling window size
            
        Returns:
            List of rolling VWAP values
        """
        if not ohlcv_data or window <= 0:
            return []
        
        rolling_vwap_values = []
        
        for i in range(len(ohlcv_data)):
            # Determine window start
            start_idx = max(0, i - window + 1)
            window_data = ohlcv_data[start_idx:i+1]
            
            # Calculate VWAP for window
            total_pv = 0.0
            total_volume = 0.0
            
            for bar in window_data:
                typical_price = self.calculate_typical_price(
                    bar['high'], bar['low'], bar['close']
                )
                pv = typical_price * bar['volume']
                total_pv += pv
                total_volume += bar['volume']
            
            if total_volume > 0:
                rolling_vwap = total_pv / total_volume
            else:
                rolling_vwap = 0.0
            
            rolling_vwap_values.append(rolling_vwap)
        
        return rolling_vwap_values
    
    def calculate_vwap_deviation(self, prices, vwap_values):
        """
        Calculate price deviation from VWAP as percentage.
        
        Args:
            prices: List of prices
            vwap_values: List of VWAP values
            
        Returns:
            List of deviation percentages
        """
        if len(prices) != len(vwap_values):
            return []
        
        deviations = []
        for price, vwap in zip(prices, vwap_values):
            if vwap != 0:
                deviation = ((price - vwap) / vwap) * 100
            else:
                deviation = 0.0
            deviations.append(deviation)
        
        return deviations
    
    def analyze_vwap_signals(self, prices, vwap_values):
        """
        Generate basic VWAP trading signals.
        
        Args:
            prices: List of prices
            vwap_values: List of VWAP values
            
        Returns:
            Dictionary with signal analysis
        """
        if len(prices) != len(vwap_values) or len(prices) < 2:
            return {}
        
        above_vwap = sum(1 for p, v in zip(prices, vwap_values) if p > v)
        below_vwap = len(prices) - above_vwap
        
        # Crossover signals
        crossovers_up = 0
        crossovers_down = 0
        
        for i in range(1, len(prices)):
            prev_above = prices[i-1] > vwap_values[i-1]
            curr_above = prices[i] > vwap_values[i]
            
            if not prev_above and curr_above:
                crossovers_up += 1
            elif prev_above and not curr_above:
                crossovers_down += 1
        
        return {
            'periods_above_vwap': above_vwap,
            'periods_below_vwap': below_vwap,
            'percent_above_vwap': (above_vwap / len(prices)) * 100,
            'bullish_crossovers': crossovers_up,
            'bearish_crossovers': crossovers_down,
            'total_periods': len(prices)
        }


def generate_sample_data(num_bars=100, base_price=100.0, seed=42):
    """Generate sample OHLCV data for testing."""
    random.seed(seed)
    
    data = []
    current_price = base_price
    
    for i in range(num_bars):
        # Generate price movement
        price_change = random.uniform(-2.0, 2.0)
        current_price += price_change
        current_price = max(current_price, 10.0)  # Minimum price
        
        # Generate OHLC
        open_price = current_price + random.uniform(-0.5, 0.5)
        close_price = current_price + random.uniform(-0.5, 0.5)
        
        high = max(open_price, close_price) + random.uniform(0, 1.0)
        low = min(open_price, close_price) - random.uniform(0, 1.0)
        
        # Generate volume (higher volume during certain periods)
        if i % 20 < 5:  # Higher volume periods
            volume = random.randint(50000, 200000)
        else:
            volume = random.randint(10000, 50000)
        
        data.append({
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': volume,
            'bar_number': i + 1
        })
    
    return data


def demonstrate_basic_vwap():
    """Demonstrate basic VWAP calculation."""
    print("="*60)
    print("BASIC VWAP CALCULATION DEMONSTRATION")
    print("="*60)
    
    # Generate sample data
    data = generate_sample_data(num_bars=50, base_price=150.0)
    calculator = SimpleVWAPCalculator()
    
    print(f"Generated {len(data)} bars of sample data")
    print(f"Price range: ${min(bar['low'] for bar in data):.2f} - ${max(bar['high'] for bar in data):.2f}")
    print(f"Volume range: {min(bar['volume'] for bar in data):,} - {max(bar['volume'] for bar in data):,}")
    
    # Calculate cumulative VWAP
    vwap_values = calculator.calculate_cumulative_vwap(data)
    
    print(f"\nVWAP Analysis:")
    print(f"Initial VWAP: ${vwap_values[0]:.2f}")
    print(f"Final VWAP: ${vwap_values[-1]:.2f}")
    print(f"Final Close: ${data[-1]['close']:.2f}")
    
    # Calculate deviation
    close_prices = [bar['close'] for bar in data]
    deviations = calculator.calculate_vwap_deviation(close_prices, vwap_values)
    
    final_deviation = deviations[-1]
    print(f"Final deviation from VWAP: {final_deviation:+.2f}%")
    
    # Show sample calculations for first few bars
    print(f"\nSample VWAP Calculations (First 5 bars):")
    print(f"{'Bar':<4} {'Typical':<8} {'Volume':<8} {'PV':<12} {'Cum PV':<12} {'Cum Vol':<10} {'VWAP':<8}")
    print("-" * 70)
    
    cumulative_pv = 0
    cumulative_vol = 0
    
    for i in range(min(5, len(data))):
        bar = data[i]
        typical_price = calculator.calculate_typical_price(bar['high'], bar['low'], bar['close'])
        pv = typical_price * bar['volume']
        cumulative_pv += pv
        cumulative_vol += bar['volume']
        vwap = cumulative_pv / cumulative_vol
        
        print(f"{i+1:<4} ${typical_price:<7.2f} {bar['volume']:<8,} ${pv:<11,.0f} ${cumulative_pv:<11,.0f} {cumulative_vol:<10,} ${vwap:<7.2f}")
    
    return data, vwap_values


def demonstrate_rolling_vwap():
    """Demonstrate rolling VWAP calculation."""
    print("\n" + "="*60)
    print("ROLLING VWAP DEMONSTRATION")
    print("="*60)
    
    # Generate sample data
    data = generate_sample_data(num_bars=30, base_price=100.0, seed=123)
    calculator = SimpleVWAPCalculator()
    
    # Calculate different rolling windows
    windows = [5, 10, 20]
    
    print(f"Calculating rolling VWAP with windows: {windows}")
    
    rolling_vwaps = {}
    for window in windows:
        rolling_vwaps[window] = calculator.calculate_rolling_vwap(data, window)
    
    # Show comparison for last 10 bars
    print(f"\nRolling VWAP Comparison (Last 10 bars):")
    print(f"{'Bar':<4} {'Close':<8} {'VWAP-5':<8} {'VWAP-10':<9} {'VWAP-20':<9}")
    print("-" * 50)
    
    start_idx = max(0, len(data) - 10)
    for i in range(start_idx, len(data)):
        bar = data[i]
        print(f"{i+1:<4} ${bar['close']:<7.2f} ${rolling_vwaps[5][i]:<7.2f} ${rolling_vwaps[10][i]:<8.2f} ${rolling_vwaps[20][i]:<8.2f}")
    
    return rolling_vwaps


def demonstrate_vwap_signals():
    """Demonstrate VWAP trading signals."""
    print("\n" + "="*60)
    print("VWAP TRADING SIGNALS DEMONSTRATION")
    print("="*60)
    
    # Generate sample data with trend
    data = generate_sample_data(num_bars=100, base_price=120.0, seed=456)
    calculator = SimpleVWAPCalculator()
    
    # Calculate VWAP
    vwap_values = calculator.calculate_cumulative_vwap(data)
    close_prices = [bar['close'] for bar in data]
    
    # Analyze signals
    signals = calculator.analyze_vwap_signals(close_prices, vwap_values)
    
    print(f"VWAP Signal Analysis:")
    print(f"  Total periods: {signals['total_periods']}")
    print(f"  Periods above VWAP: {signals['periods_above_vwap']} ({signals['percent_above_vwap']:.1f}%)")
    print(f"  Periods below VWAP: {signals['periods_below_vwap']} ({100-signals['percent_above_vwap']:.1f}%)")
    print(f"  Bullish crossovers: {signals['bullish_crossovers']}")
    print(f"  Bearish crossovers: {signals['bearish_crossovers']}")
    
    # Calculate average deviations
    deviations = calculator.calculate_vwap_deviation(close_prices, vwap_values)
    positive_deviations = [d for d in deviations if d > 0]
    negative_deviations = [d for d in deviations if d < 0]
    
    print(f"\nDeviation Analysis:")
    if positive_deviations:
        print(f"  Average positive deviation: +{sum(positive_deviations)/len(positive_deviations):.2f}%")
        print(f"  Maximum positive deviation: +{max(positive_deviations):.2f}%")
    
    if negative_deviations:
        print(f"  Average negative deviation: {sum(negative_deviations)/len(negative_deviations):.2f}%")
        print(f"  Maximum negative deviation: {min(negative_deviations):.2f}%")
    
    # Show recent crossovers
    print(f"\nRecent Crossover Events:")
    crossover_events = []
    
    for i in range(1, len(close_prices)):
        prev_above = close_prices[i-1] > vwap_values[i-1]
        curr_above = close_prices[i] > vwap_values[i]
        
        if not prev_above and curr_above:
            crossover_events.append((i+1, 'BULLISH', close_prices[i], vwap_values[i]))
        elif prev_above and not curr_above:
            crossover_events.append((i+1, 'BEARISH', close_prices[i], vwap_values[i]))
    
    # Show last 5 crossovers
    recent_crossovers = crossover_events[-5:] if len(crossover_events) >= 5 else crossover_events
    
    if recent_crossovers:
        print(f"  {'Bar':<4} {'Type':<8} {'Price':<8} {'VWAP':<8} {'Signal'}")
        print("  " + "-" * 40)
        for bar, signal_type, price, vwap in recent_crossovers:
            signal_desc = "Price crossed above VWAP" if signal_type == 'BULLISH' else "Price crossed below VWAP"
            print(f"  {bar:<4} {signal_type:<8} ${price:<7.2f} ${vwap:<7.2f} {signal_desc}")
    else:
        print("  No crossover events found in the data")
    
    return signals


def demonstrate_execution_analysis():
    """Demonstrate VWAP execution analysis."""
    print("\n" + "="*60)
    print("VWAP EXECUTION ANALYSIS DEMONSTRATION")
    print("="*60)
    
    # Simulate a trading day
    data = generate_sample_data(num_bars=50, base_price=200.0, seed=789)
    calculator = SimpleVWAPCalculator()
    
    # Calculate benchmark VWAP
    vwap_values = calculator.calculate_cumulative_vwap(data)
    benchmark_vwap = vwap_values[-1]
    
    print(f"Benchmark VWAP (end of day): ${benchmark_vwap:.2f}")
    
    # Simulate trade executions
    random.seed(999)
    executions = []
    
    # Generate realistic executions with some slippage
    for i in range(8):
        execution_price = benchmark_vwap + random.uniform(-0.10, 0.15)  # Some slippage
        execution_volume = random.randint(1000, 5000)
        executions.append((execution_price, execution_volume))
    
    print(f"\nSimulated Trade Executions:")
    print(f"{'#':<3} {'Price':<8} {'Volume':<8} {'Slippage':<10}")
    print("-" * 35)
    
    total_pv = 0
    total_volume = 0
    
    for i, (price, volume) in enumerate(executions, 1):
        slippage = price - benchmark_vwap
        total_pv += price * volume
        total_volume += volume
        print(f"{i:<3} ${price:<7.2f} {volume:<8,} ${slippage:+.3f}")
    
    # Calculate execution VWAP
    execution_vwap = total_pv / total_volume if total_volume > 0 else 0
    
    print(f"\nExecution Analysis:")
    print(f"  Execution VWAP: ${execution_vwap:.2f}")
    print(f"  Benchmark VWAP: ${benchmark_vwap:.2f}")
    print(f"  VWAP Slippage: ${execution_vwap - benchmark_vwap:+.3f}")
    print(f"  Slippage %: {((execution_vwap / benchmark_vwap) - 1) * 100:+.2f}%")
    print(f"  Total Volume: {total_volume:,} shares")
    print(f"  Number of Executions: {len(executions)}")
    
    # Performance assessment
    slippage_pct = ((execution_vwap / benchmark_vwap) - 1) * 100
    
    if abs(slippage_pct) < 0.05:
        performance = "EXCELLENT"
    elif abs(slippage_pct) < 0.1:
        performance = "GOOD"
    elif abs(slippage_pct) < 0.2:
        performance = "ACCEPTABLE"
    else:
        performance = "POOR"
    
    print(f"  Performance Rating: {performance}")
    
    return execution_vwap, benchmark_vwap


def main():
    """Run all VWAP demonstrations."""
    print("SIMPLE VWAP CALCULATOR DEMONSTRATION")
    print("Volume Weighted Average Price - Core Concepts and Applications")
    print("="*80)
    
    try:
        # Run demonstrations
        demonstrate_basic_vwap()
        demonstrate_rolling_vwap()
        demonstrate_vwap_signals()
        demonstrate_execution_analysis()
        
        print("\n" + "="*80)
        print("VWAP DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("="*80)
        
        print("\nKey VWAP Concepts Demonstrated:")
        print("1. Basic VWAP Calculation: Σ(Price × Volume) / Σ(Volume)")
        print("2. Typical Price Usage: (High + Low + Close) / 3")
        print("3. Cumulative vs Rolling VWAP")
        print("4. Price Deviation Analysis")
        print("5. Trading Signal Generation")
        print("6. Execution Performance Analysis")
        
        print("\nVWAP Applications in Trading:")
        print("• Execution Benchmarking - Compare trade prices to VWAP")
        print("• Support/Resistance Levels - VWAP acts as dynamic S/R")
        print("• Trend Identification - Price above/below VWAP indicates trend")
        print("• Mean Reversion Trading - Price tends to revert to VWAP")
        print("• Volume-Weighted Analysis - Incorporates volume in price analysis")
        
        print("\nNext Steps:")
        print("1. Install full dependencies: pip install -r requirements.txt")
        print("2. Run advanced VWAP analysis: python examples/vwap_analysis.py")
        print("3. Integrate VWAP with stock optimization system")
        print("4. Test with real market data using Yahoo Finance")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
