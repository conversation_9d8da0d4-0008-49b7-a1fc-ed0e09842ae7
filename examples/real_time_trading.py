"""
Real-Time Trading System Example

This example demonstrates the real-time trading system with continuous
portfolio optimization and rebalancing.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.data_provider import YahooDataProvider, SyntheticDataProvider
from src.stock_optimizer import StockOptimizer
from src.trading_system import RealTimeTradingSystem
from src.risk_manager import RiskManager
import time
import signal
import threading


class TradingSystemDemo:
    """Demo class for the real-time trading system."""
    
    def __init__(self):
        self.trading_system = None
        self.running = True
        
    def setup_system(self):
        """Setup the trading system components."""
        print("Setting up real-time trading system...")
        
        # Use synthetic data for demo (replace with YahooDataProvider for real data)
        data_provider = SyntheticDataProvider(n_stocks=30, seed=42)
        
        # Initialize optimizer
        optimizer = StockOptimizer(
            data_provider=data_provider,
            lambda_correlation=1.5,
            lambda_balance=1.0,
            lambda_constraints=10.0,
            solver_type='simulated_annealing'
        )
        
        # Initialize risk manager
        risk_manager = RiskManager(
            max_portfolio_volatility=0.25,
            max_individual_weight=0.20,
            max_sector_weight=0.40,
            max_drawdown_threshold=0.15
        )
        
        # Create stock universe (using synthetic stocks)
        universe = data_provider.symbols[:20]  # Use first 20 stocks
        
        # Initialize trading system
        self.trading_system = RealTimeTradingSystem(
            data_provider=data_provider,
            optimizer=optimizer,
            risk_manager=risk_manager,
            universe=universe,
            target_stocks=8,
            rebalance_frequency='daily',  # For demo, use daily
            lookback_days=100,
            max_turnover=0.4,
            min_turnover=0.1
        )
        
        # Set up callbacks
        self.trading_system.set_callbacks(
            on_rebalance=self.on_rebalance,
            on_trade=self.on_trade,
            on_error=self.on_error
        )
        
        print("Trading system setup completed!")
        
    def on_rebalance(self, trade_record):
        """Callback for rebalancing events."""
        print("\n" + "="*50)
        print("PORTFOLIO REBALANCED")
        print("="*50)
        print(f"Timestamp: {trade_record['timestamp']}")
        print(f"Old portfolio: {trade_record['old_portfolio']}")
        print(f"New portfolio: {trade_record['new_portfolio']}")
        print(f"Stocks bought: {trade_record['stocks_bought']}")
        print(f"Stocks sold: {trade_record['stocks_sold']}")
        print(f"Turnover: {trade_record['turnover']:.2%}")
        
        # Risk assessment
        risk_assessment = trade_record['risk_assessment']
        print(f"Risk level: {risk_assessment['risk_level']}")
        
        # Optimization metrics
        opt_result = trade_record['optimization_result']
        obj_comp = opt_result['objective_components']
        print(f"Expected return: {obj_comp['expected_return']:.4f}")
        print(f"Average correlation: {obj_comp['avg_correlation']:.4f}")
        print("="*50)
        
    def on_trade(self, trade):
        """Callback for individual trades."""
        print(f"TRADE: {trade['action']} {trade['symbol']} at ${trade['price']:.2f} - {trade['status']}")
        
    def on_error(self, error):
        """Callback for errors."""
        print(f"ERROR: {error}")
        
    def run_demo(self, duration_minutes=5):
        """Run the trading system demo."""
        print(f"\nStarting trading system demo for {duration_minutes} minutes...")
        print("Press Ctrl+C to stop early")
        
        # Start with an initial portfolio
        initial_portfolio = self.trading_system.universe[:self.trading_system.target_stocks]
        self.trading_system.start(initial_portfolio=initial_portfolio)
        
        try:
            # Monitor system for specified duration
            start_time = time.time()
            while self.running and (time.time() - start_time) < duration_minutes * 60:
                # Display system status every 30 seconds
                time.sleep(30)
                self.display_status()
                
        except KeyboardInterrupt:
            print("\nDemo interrupted by user")
        finally:
            self.trading_system.stop()
            print("Trading system stopped")
            
    def display_status(self):
        """Display current system status."""
        status = self.trading_system.get_portfolio_status()
        
        print("\n" + "-"*40)
        print("SYSTEM STATUS")
        print("-"*40)
        print(f"Running: {status['is_running']}")
        print(f"Current portfolio: {status['current_portfolio']}")
        print(f"Last rebalance: {status['last_rebalance']}")
        
        if status['performance_metrics']:
            perf = status['performance_metrics']
            print(f"Portfolio return: {perf.get('total_return', 0):.2%}")
            print(f"Volatility: {perf.get('volatility', 0):.2%}")
            print(f"Sharpe ratio: {perf.get('sharpe_ratio', 0):.4f}")
            
        print(f"Trading history entries: {status['trading_history_length']}")
        print("-"*40)
        
    def signal_handler(self, signum, frame):
        """Handle interrupt signals."""
        print("\nReceived interrupt signal, stopping...")
        self.running = False


def main():
    """Main function to run the demo."""
    print("=" * 60)
    print("REAL-TIME TRADING SYSTEM DEMO")
    print("=" * 60)
    
    demo = TradingSystemDemo()
    
    # Setup signal handler for graceful shutdown
    signal.signal(signal.SIGINT, demo.signal_handler)
    
    try:
        # Setup the trading system
        demo.setup_system()
        
        # Run the demo
        demo.run_demo(duration_minutes=2)  # Short demo for testing
        
        # Display final results
        print("\n" + "=" * 60)
        print("FINAL RESULTS")
        print("=" * 60)
        
        final_status = demo.trading_system.get_portfolio_status()
        print(f"Final portfolio: {final_status['current_portfolio']}")
        print(f"Total rebalances: {final_status['trading_history_length']}")
        
        if final_status['performance_metrics']:
            perf = final_status['performance_metrics']
            print(f"Final performance metrics:")
            print(f"  Total return: {perf.get('total_return', 0):.2%}")
            print(f"  Volatility: {perf.get('volatility', 0):.2%}")
            print(f"  Sharpe ratio: {perf.get('sharpe_ratio', 0):.4f}")
            print(f"  Max drawdown: {perf.get('max_drawdown', 0):.2%}")
        
        # Show trading history summary
        if hasattr(demo.trading_system, 'trading_history') and demo.trading_system.trading_history:
            print(f"\nTrading History Summary:")
            for i, record in enumerate(demo.trading_system.trading_history, 1):
                print(f"  Rebalance {i}: {len(record['stocks_bought'])} bought, "
                      f"{len(record['stocks_sold'])} sold, "
                      f"turnover: {record['turnover']:.1%}")
        
    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETED")
    print("=" * 60)


if __name__ == "__main__":
    main()
