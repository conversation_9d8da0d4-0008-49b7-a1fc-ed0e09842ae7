"""
VWAP Analysis Example

This example demonstrates various VWAP calculations and their applications
in trading analysis and execution benchmarking.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

from src.vwap_calculator import VWAPCalculator
from src.data_provider import YahooDataProvider


def create_sample_intraday_data(symbol='AAPL', days=5):
    """Create sample intraday data for VWAP demonstration."""
    # Generate sample intraday data
    np.random.seed(42)
    
    # Create minute-by-minute data for specified days
    start_date = datetime.now() - timedelta(days=days)
    
    # Trading hours: 9:30 AM to 4:00 PM (6.5 hours = 390 minutes per day)
    minutes_per_day = 390
    total_minutes = days * minutes_per_day
    
    # Generate timestamps for trading hours only
    timestamps = []
    current_date = start_date.date()
    
    for day in range(days):
        day_start = datetime.combine(current_date, datetime.strptime('09:30', '%H:%M').time())
        for minute in range(minutes_per_day):
            timestamps.append(day_start + timedelta(minutes=minute))
        current_date = current_date + timedelta(days=1)
    
    # Generate realistic OHLCV data
    base_price = 150.0
    prices = []
    volumes = []
    
    current_price = base_price
    
    for i in range(total_minutes):
        # Price movement with some trend and noise
        price_change = np.random.normal(0, 0.1) + 0.001 * np.sin(i / 100)  # Small upward trend
        current_price += price_change
        
        # Generate OHLC for the minute
        high = current_price + abs(np.random.normal(0, 0.05))
        low = current_price - abs(np.random.normal(0, 0.05))
        open_price = current_price + np.random.normal(0, 0.02)
        close_price = current_price + np.random.normal(0, 0.02)
        
        # Ensure OHLC relationships are maintained
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)
        
        prices.append([open_price, high, low, close_price])
        
        # Volume with higher volume during market open/close
        hour = timestamps[i].hour
        if hour in [9, 10, 15]:  # Higher volume at open and close
            base_volume = np.random.randint(50000, 200000)
        else:
            base_volume = np.random.randint(10000, 50000)
        
        volumes.append(base_volume)
    
    # Create DataFrame
    data = pd.DataFrame({
        'Open': [p[0] for p in prices],
        'High': [p[1] for p in prices],
        'Low': [p[2] for p in prices],
        'Close': [p[3] for p in prices],
        'Volume': volumes
    }, index=pd.DatetimeIndex(timestamps))
    
    return data


def demonstrate_basic_vwap():
    """Demonstrate basic VWAP calculations."""
    print("\n" + "="*60)
    print("BASIC VWAP CALCULATIONS")
    print("="*60)
    
    # Create sample data
    data = create_sample_intraday_data(days=3)
    vwap_calc = VWAPCalculator()
    
    print(f"Sample data shape: {data.shape}")
    print(f"Date range: {data.index[0]} to {data.index[-1]}")
    print(f"Price range: ${data['Close'].min():.2f} - ${data['Close'].max():.2f}")
    print(f"Average volume: {data['Volume'].mean():,.0f}")
    
    # Calculate different types of VWAP
    print(f"\n1. Standard VWAP (cumulative)")
    standard_vwap = vwap_calc.calculate_vwap(data)
    print(f"   Final VWAP: ${standard_vwap.iloc[-1]:.2f}")
    print(f"   Final Close: ${data['Close'].iloc[-1]:.2f}")
    print(f"   Price vs VWAP: {((data['Close'].iloc[-1] / standard_vwap.iloc[-1]) - 1) * 100:.2f}%")
    
    print(f"\n2. Rolling VWAP (50-period)")
    rolling_vwap = vwap_calc.calculate_rolling_vwap(data, window=50)
    print(f"   Final Rolling VWAP: ${rolling_vwap.iloc[-1]:.2f}")
    
    print(f"\n3. Session VWAP (daily reset)")
    session_vwap = vwap_calc.calculate_session_vwap(data)
    print(f"   Final Session VWAP: ${session_vwap.iloc[-1]:.2f}")
    
    # Calculate VWAP bands
    print(f"\n4. VWAP Bands")
    bands = vwap_calc.calculate_vwap_bands(standard_vwap, data, num_std=1.0)
    print(f"   Upper Band: ${bands['upper_band'].iloc[-1]:.2f}")
    print(f"   Lower Band: ${bands['lower_band'].iloc[-1]:.2f}")
    print(f"   Band Width: ${bands['upper_band'].iloc[-1] - bands['lower_band'].iloc[-1]:.2f}")
    
    return data, {
        'standard_vwap': standard_vwap,
        'rolling_vwap': rolling_vwap,
        'session_vwap': session_vwap,
        'bands': bands
    }


def demonstrate_vwap_signals():
    """Demonstrate VWAP-based trading signals."""
    print("\n" + "="*60)
    print("VWAP TRADING SIGNALS")
    print("="*60)
    
    # Create sample data
    data = create_sample_intraday_data(days=2)
    vwap_calc = VWAPCalculator()
    
    # Calculate VWAP and signals
    vwap = vwap_calc.calculate_vwap(data)
    signals = vwap_calc.calculate_vwap_signals(data, vwap)
    
    # Analyze signals
    print(f"Signal Analysis:")
    print(f"  Time above VWAP: {signals['above_vwap'].sum()} / {len(signals['above_vwap'])} periods ({signals['above_vwap'].mean()*100:.1f}%)")
    print(f"  Time below VWAP: {signals['below_vwap'].sum()} / {len(signals['below_vwap'])} periods ({signals['below_vwap'].mean()*100:.1f}%)")
    print(f"  Bullish crossovers: {signals['cross_above'].sum()}")
    print(f"  Bearish crossovers: {signals['cross_below'].sum()}")
    
    # Distance analysis
    avg_distance = signals['distance_pct'].mean()
    max_distance_above = signals['distance_pct'].max()
    max_distance_below = signals['distance_pct'].min()
    
    print(f"\nDistance from VWAP:")
    print(f"  Average distance: {avg_distance:.2f}%")
    print(f"  Max distance above: {max_distance_above:.2f}%")
    print(f"  Max distance below: {max_distance_below:.2f}%")
    
    # VWAP trend analysis
    trend_up_periods = signals['trend_up'].sum()
    trend_down_periods = signals['trend_down'].sum()
    
    print(f"\nVWAP Trend Analysis:")
    print(f"  Uptrend periods: {trend_up_periods} ({trend_up_periods/len(signals['trend_up'])*100:.1f}%)")
    print(f"  Downtrend periods: {trend_down_periods} ({trend_down_periods/len(signals['trend_down'])*100:.1f}%)")
    
    return signals


def demonstrate_execution_analysis():
    """Demonstrate VWAP execution analysis."""
    print("\n" + "="*60)
    print("VWAP EXECUTION ANALYSIS")
    print("="*60)
    
    # Create sample data
    data = create_sample_intraday_data(days=1)
    vwap_calc = VWAPCalculator()
    
    # Calculate benchmark VWAP
    benchmark_vwap = vwap_calc.calculate_vwap(data).iloc[-1]
    
    print(f"Benchmark VWAP: ${benchmark_vwap:.2f}")
    
    # Simulate trade executions
    np.random.seed(123)
    num_executions = 10
    
    # Generate execution prices around the benchmark with some slippage
    execution_prices = []
    execution_volumes = []
    
    for i in range(num_executions):
        # Simulate realistic execution slippage
        slippage = np.random.normal(0.02, 0.05)  # Average 2 cents slippage with 5 cent std
        execution_price = benchmark_vwap + slippage
        execution_volume = np.random.randint(1000, 5000)
        
        execution_prices.append(execution_price)
        execution_volumes.append(execution_volume)
    
    print(f"\nSimulated Executions:")
    for i, (price, volume) in enumerate(zip(execution_prices, execution_volumes)):
        slippage = price - benchmark_vwap
        print(f"  Execution {i+1}: ${price:.2f} ({volume:,} shares) - Slippage: ${slippage:.3f}")
    
    # Analyze execution performance
    analysis = vwap_calc.vwap_execution_analysis(
        execution_prices=execution_prices,
        execution_volumes=execution_volumes,
        benchmark_vwap=benchmark_vwap
    )
    
    print(f"\nExecution Analysis:")
    print(f"  Execution VWAP: ${analysis['execution_vwap']:.2f}")
    print(f"  Benchmark VWAP: ${analysis['benchmark_vwap']:.2f}")
    print(f"  VWAP Slippage: ${analysis['vwap_slippage']:.3f} ({analysis['vwap_slippage_pct']:.2f}%)")
    print(f"  Average Price: ${analysis['avg_execution_price']:.2f}")
    print(f"  Price Std Dev: ${analysis['price_std']:.3f}")
    print(f"  Total Volume: {analysis['total_volume']:,} shares")
    print(f"  Number of Executions: {analysis['num_executions']}")
    
    # Performance assessment
    if analysis['vwap_slippage_pct'] < 0:
        performance = "GOOD (Better than VWAP)"
    elif analysis['vwap_slippage_pct'] < 0.1:
        performance = "ACCEPTABLE (Close to VWAP)"
    else:
        performance = "POOR (Significant slippage)"
    
    print(f"  Performance Assessment: {performance}")
    
    return analysis


def demonstrate_multiple_timeframes():
    """Demonstrate VWAP across multiple timeframes."""
    print("\n" + "="*60)
    print("MULTIPLE TIMEFRAME VWAP ANALYSIS")
    print("="*60)
    
    # Create sample data
    data = create_sample_intraday_data(days=5)
    vwap_calc = VWAPCalculator()
    
    # Calculate multiple rolling VWAPs
    windows = [20, 50, 100, 200]
    vwaps = vwap_calc.calculate_multiple_vwaps(data, windows=windows)
    
    print(f"Multiple VWAP Analysis (Final Values):")
    current_price = data['Close'].iloc[-1]
    print(f"  Current Price: ${current_price:.2f}")
    
    for window in windows:
        vwap_key = f'vwap_{window}'
        vwap_value = vwaps[vwap_key].iloc[-1]
        deviation = ((current_price / vwap_value) - 1) * 100
        
        print(f"  VWAP-{window}: ${vwap_value:.2f} (Deviation: {deviation:+.2f}%)")
    
    # Analyze VWAP alignment
    print(f"\nVWAP Alignment Analysis:")
    vwap_values = [vwaps[f'vwap_{w}'].iloc[-1] for w in windows]
    
    if all(vwap_values[i] <= vwap_values[i+1] for i in range(len(vwap_values)-1)):
        alignment = "BULLISH (Shorter > Longer timeframes)"
    elif all(vwap_values[i] >= vwap_values[i+1] for i in range(len(vwap_values)-1)):
        alignment = "BEARISH (Shorter < Longer timeframes)"
    else:
        alignment = "MIXED (No clear trend)"
    
    print(f"  Trend Alignment: {alignment}")
    
    return vwaps


def create_vwap_visualization(data, vwap_results):
    """Create VWAP visualization."""
    print("\n" + "="*60)
    print("CREATING VWAP VISUALIZATION")
    print("="*60)
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('VWAP Analysis Dashboard', fontsize=16, fontweight='bold')
        
        # Plot 1: Price vs VWAP
        axes[0, 0].plot(data.index, data['Close'], label='Close Price', linewidth=1, alpha=0.8)
        axes[0, 0].plot(data.index, vwap_results['standard_vwap'], label='VWAP', linewidth=2, color='red')
        axes[0, 0].fill_between(data.index, 
                               vwap_results['bands']['lower_band'], 
                               vwap_results['bands']['upper_band'], 
                               alpha=0.2, color='red', label='VWAP Bands')
        axes[0, 0].set_title('Price vs VWAP with Bands')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Plot 2: Multiple VWAPs
        axes[0, 1].plot(data.index, data['Close'], label='Close Price', linewidth=1, alpha=0.8)
        axes[0, 1].plot(data.index, vwap_results['standard_vwap'], label='Standard VWAP', linewidth=2)
        axes[0, 1].plot(data.index, vwap_results['rolling_vwap'], label='Rolling VWAP (50)', linewidth=2)
        axes[0, 1].plot(data.index, vwap_results['session_vwap'], label='Session VWAP', linewidth=2)
        axes[0, 1].set_title('Multiple VWAP Types')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Plot 3: Volume Profile
        axes[1, 0].bar(data.index, data['Volume'], alpha=0.6, width=0.0008)
        axes[1, 0].set_title('Volume Profile')
        axes[1, 0].set_ylabel('Volume')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Plot 4: Price Distance from VWAP
        vwap_calc = VWAPCalculator()
        signals = vwap_calc.calculate_vwap_signals(data, vwap_results['standard_vwap'])
        axes[1, 1].plot(data.index, signals['distance_pct'], linewidth=1)
        axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[1, 1].fill_between(data.index, signals['distance_pct'], 0, 
                               where=(signals['distance_pct'] > 0), alpha=0.3, color='green', label='Above VWAP')
        axes[1, 1].fill_between(data.index, signals['distance_pct'], 0, 
                               where=(signals['distance_pct'] < 0), alpha=0.3, color='red', label='Below VWAP')
        axes[1, 1].set_title('Price Distance from VWAP (%)')
        axes[1, 1].set_ylabel('Distance %')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('vwap_analysis.png', dpi=300, bbox_inches='tight')
        print("VWAP visualization saved as 'vwap_analysis.png'")
        plt.show()
        
    except Exception as e:
        print(f"Could not create visualization: {e}")


def main():
    """Run all VWAP demonstrations."""
    print("VWAP CALCULATOR DEMONSTRATION")
    print("Volume Weighted Average Price Analysis and Applications")
    print("="*80)
    
    try:
        # Run demonstrations
        data, vwap_results = demonstrate_basic_vwap()
        demonstrate_vwap_signals()
        demonstrate_execution_analysis()
        demonstrate_multiple_timeframes()
        
        # Create visualization
        create_vwap_visualization(data, vwap_results)
        
        print("\n" + "="*80)
        print("VWAP DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("="*80)
        
        print("\nKey VWAP Applications:")
        print("1. Execution Benchmarking - Compare trade execution against VWAP")
        print("2. Support/Resistance - VWAP acts as dynamic support/resistance")
        print("3. Trend Analysis - VWAP slope indicates price trend direction")
        print("4. Mean Reversion - Price tends to revert to VWAP over time")
        print("5. Volume Analysis - Incorporates volume in price analysis")
        
        print("\nVWAP Types Implemented:")
        print("• Standard VWAP - Cumulative from start of period")
        print("• Rolling VWAP - Moving window calculation")
        print("• Session VWAP - Resets each trading day")
        print("• Anchored VWAP - From specific date/event")
        print("• VWAP Bands - Standard deviation bands around VWAP")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
