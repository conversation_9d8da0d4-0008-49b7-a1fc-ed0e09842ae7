"""
Basic Stock Optimization Example

This example demonstrates the basic usage of the stock optimization system
using OpenJij for solving the QUBO formulation.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.data_provider import YahooDataProvider, SyntheticDataProvider, create_sample_data
from src.stock_optimizer import StockOptimizer
from src.utils import plot_portfolio_analysis, create_optimization_report
import pandas as pd
import numpy as np


def main():
    print("=" * 60)
    print("BASIC STOCK OPTIMIZATION EXAMPLE")
    print("=" * 60)
    
    # Option 1: Use synthetic data for testing
    print("\n1. Creating synthetic test data...")
    returns, sector_info = create_sample_data(n_stocks=20, n_days=252)
    data_provider = SyntheticDataProvider(n_stocks=20)
    
    print(f"Generated data for {len(returns.columns)} stocks over {len(returns)} days")
    print(f"Sector distribution: {set(sector_info.values())}")
    
    # Option 2: Uncomment to use real Yahoo Finance data
    # print("\n1. Setting up Yahoo Finance data provider...")
    # data_provider = YahooDataProvider()
    # universe = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'V', 'UNH']
    # returns = data_provider.get_returns(universe, '2022-01-01', '2023-12-31')
    # sector_info = data_provider.get_sector_info(universe)
    
    # Initialize optimizer with different penalty weights
    print("\n2. Initializing stock optimizer...")
    optimizer = StockOptimizer(
        data_provider=data_provider,
        lambda_correlation=2.0,    # Higher penalty for correlation
        lambda_balance=1.0,        # Standard balance penalty
        lambda_constraints=10.0,   # Strong constraint enforcement
        solver_type='simulated_annealing'
    )
    
    # Define optimization parameters
    universe = list(returns.columns)
    target_stocks = 5
    
    print(f"Universe size: {len(universe)}")
    print(f"Target portfolio size: {target_stocks}")
    
    # Perform optimization
    print("\n3. Running optimization...")
    try:
        result = optimizer.select_stocks(
            universe=universe,
            target_stocks=target_stocks,
            lookback_days=200,
            sector_info=sector_info,
            max_sector_weight=0.6,
            num_reads=1000,
            num_sweeps=2000  # Additional solver parameter
        )
        
        print("Optimization completed successfully!")
        
        # Display results
        print("\n4. OPTIMIZATION RESULTS")
        print("-" * 40)
        print(f"Selected stocks: {result['selected_stocks']}")
        print(f"Optimization energy: {result['energy']:.6f}")
        
        # Objective function breakdown
        obj_comp = result['objective_components']
        print(f"\nObjective Function Components:")
        print(f"  Expected Return: {obj_comp['expected_return']:.4f}")
        print(f"  Average Correlation: {obj_comp['avg_correlation']:.4f}")
        print(f"  Total Objective: {obj_comp['total_objective']:.6f}")
        
        # Validation
        validation = result['validation']
        print(f"\nConstraint Validation:")
        print(f"  Number of stocks OK: {validation['num_stocks_ok']}")
        print(f"  Sector constraints OK: {validation['sector_constraints_ok']}")
        print(f"  Sector distribution: {validation['sector_distribution']}")
        
        # Solver performance
        solver_info = result['solver_info']
        energy_stats = solver_info['energy_statistics']
        print(f"\nSolver Performance:")
        print(f"  Solver type: {solver_info['solver_type']}")
        print(f"  Number of reads: {solver_info['num_reads']}")
        print(f"  Best energy: {solver_info['best_energy']:.6f}")
        print(f"  Energy std: {energy_stats['std']:.6f}")
        
        # Create detailed report
        print("\n5. Generating detailed report...")
        report = create_optimization_report(result)
        print("\nDETAILED REPORT:")
        print(report)
        
        # Portfolio analysis visualization
        print("\n6. Creating portfolio analysis plots...")
        if len(result['selected_stocks']) > 0:
            weights = {stock: 1.0/len(result['selected_stocks']) 
                      for stock in result['selected_stocks']}
            
            try:
                plot_portfolio_analysis(
                    returns=result['returns_data'],
                    weights=weights,
                    selected_stocks=result['selected_stocks'],
                    save_path='portfolio_analysis.png'
                )
                print("Portfolio analysis plots saved as 'portfolio_analysis.png'")
            except Exception as e:
                print(f"Could not create plots: {e}")
        
        # Compare with random selection
        print("\n7. Comparison with random selection...")
        np.random.seed(42)
        random_stocks = np.random.choice(universe, target_stocks, replace=False).tolist()
        
        print(f"Random selection: {random_stocks}")
        
        # Calculate performance metrics for both portfolios
        from src.utils import calculate_portfolio_performance
        
        # Optimized portfolio
        opt_weights = {stock: 1.0/len(result['selected_stocks']) 
                      for stock in result['selected_stocks']}
        opt_performance = calculate_portfolio_performance(
            result['returns_data'], opt_weights
        )
        
        # Random portfolio
        random_returns = returns[random_stocks]
        random_weights = {stock: 1.0/len(random_stocks) for stock in random_stocks}
        random_performance = calculate_portfolio_performance(
            random_returns, random_weights
        )
        
        print(f"\nPerformance Comparison:")
        print(f"                    Optimized    Random")
        print(f"Annualized Return:  {opt_performance['annualized_return']:8.2%}  {random_performance['annualized_return']:8.2%}")
        print(f"Volatility:         {opt_performance['volatility']:8.2%}  {random_performance['volatility']:8.2%}")
        print(f"Sharpe Ratio:       {opt_performance['sharpe_ratio']:8.4f}  {random_performance['sharpe_ratio']:8.4f}")
        print(f"Max Drawdown:       {opt_performance['max_drawdown']:8.2%}  {random_performance['max_drawdown']:8.2%}")
        
        # Calculate correlation comparison
        opt_corr_matrix = result['returns_data'].corr()
        random_corr_matrix = random_returns.corr()
        
        opt_avg_corr = opt_corr_matrix.values[np.triu_indices_from(opt_corr_matrix.values, k=1)].mean()
        random_avg_corr = random_corr_matrix.values[np.triu_indices_from(random_corr_matrix.values, k=1)].mean()
        
        print(f"Avg Correlation:    {opt_avg_corr:8.4f}  {random_avg_corr:8.4f}")
        
    except Exception as e:
        print(f"Optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n" + "=" * 60)
    print("EXAMPLE COMPLETED SUCCESSFULLY")
    print("=" * 60)


if __name__ == "__main__":
    main()
