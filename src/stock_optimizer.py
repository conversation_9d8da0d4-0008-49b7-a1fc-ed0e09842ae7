"""
Stock Optimizer using OpenJij

This module implements the main stock optimization engine using OpenJij
for solving the QUBO formulation of the stock selection problem.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import openjij as oj
from .qubo_formulation import QUBOFormulation
from .data_provider import BaseDataProvider
import warnings
warnings.filterwarnings('ignore')


class StockOptimizer:
    """
    Main stock optimizer using OpenJij for QUBO solving.
    """
    
    def __init__(self, 
                 data_provider: BaseDataProvider,
                 lambda_correlation: float = 1.0,
                 lambda_balance: float = 1.0,
                 lambda_constraints: float = 10.0,
                 solver_type: str = 'simulated_annealing'):
        """
        Initialize the stock optimizer.
        
        Args:
            data_provider: Data provider instance
            lambda_correlation: Weight for correlation penalty
            lambda_balance: Weight for balance penalty
            lambda_constraints: Weight for constraint penalties
            solver_type: OpenJij solver type ('simulated_annealing', 'sqa', 'chimera')
        """
        self.data_provider = data_provider
        self.qubo_formulation = QUBOFormulation(
            lambda_correlation=lambda_correlation,
            lambda_balance=lambda_balance,
            lambda_constraints=lambda_constraints
        )
        self.solver_type = solver_type
        self.last_solution = None
        self.last_objective = None
        
    def select_stocks(self,
                     universe: List[str],
                     target_stocks: int,
                     lookback_days: int = 252,
                     end_date: Optional[str] = None,
                     sector_info: Optional[Dict[str, str]] = None,
                     max_sector_weight: float = 0.5,
                     num_reads: int = 1000,
                     **solver_kwargs) -> Dict[str, Any]:
        """
        Select optimal stocks from the universe.
        
        Args:
            universe: List of stock symbols to choose from
            target_stocks: Number of stocks to select
            lookback_days: Number of days to look back for returns calculation
            end_date: End date for data (if None, uses latest available)
            sector_info: Sector information for stocks
            max_sector_weight: Maximum weight per sector
            num_reads: Number of optimization runs
            **solver_kwargs: Additional arguments for the solver
            
        Returns:
            Dictionary with optimization results
        """
        # Calculate date range
        if end_date is None:
            end_date = pd.Timestamp.now().strftime('%Y-%m-%d')
        
        start_date = (pd.to_datetime(end_date) - pd.Timedelta(days=int(lookback_days * 1.4))).strftime('%Y-%m-%d')
        
        # Get returns data
        returns = self.data_provider.get_returns(universe, start_date, end_date)
        
        if returns.empty:
            raise ValueError("No return data available for the given universe and date range")
        
        # Filter out stocks with insufficient data
        min_observations = max(30, lookback_days // 10)  # At least 30 observations or 10% of lookback
        valid_stocks = returns.columns[returns.count() >= min_observations].tolist()
        
        if len(valid_stocks) < target_stocks:
            raise ValueError(f"Only {len(valid_stocks)} stocks have sufficient data, but {target_stocks} requested")
        
        returns = returns[valid_stocks]
        
        # Get sector information if not provided
        if sector_info is None:
            sector_info = self.data_provider.get_sector_info(valid_stocks)
        else:
            sector_info = {k: v for k, v in sector_info.items() if k in valid_stocks}
        
        # Formulate QUBO problem
        Q, linear = self.qubo_formulation.formulate_qubo(
            returns=returns,
            target_stocks=target_stocks,
            sector_info=sector_info,
            max_sector_weight=max_sector_weight
        )
        
        # Solve using OpenJij
        solution, energy, solver_info = self._solve_qubo(
            Q=Q,
            linear=linear,
            num_reads=num_reads,
            **solver_kwargs
        )
        
        # Convert solution to stock selection
        selected_stocks = [stock for i, stock in enumerate(valid_stocks) if solution[i] == 1]
        
        # Validate solution
        solution_dict = {stock: 1 if stock in selected_stocks else 0 for stock in valid_stocks}
        validation = self.qubo_formulation.validate_solution(
            solution=solution_dict,
            target_stocks=target_stocks,
            sector_info=sector_info,
            max_sector_weight=max_sector_weight
        )
        
        # Calculate objective components
        objective_components = self.qubo_formulation.calculate_objective_value(
            solution=solution_dict,
            returns=returns
        )
        
        # Store results
        self.last_solution = solution_dict
        self.last_objective = objective_components
        
        return {
            'selected_stocks': selected_stocks,
            'solution_vector': solution,
            'energy': energy,
            'objective_components': objective_components,
            'validation': validation,
            'solver_info': solver_info,
            'returns_data': returns[selected_stocks] if selected_stocks else pd.DataFrame(),
            'sector_distribution': validation.get('sector_distribution', {}),
            'universe_size': len(valid_stocks),
            'target_stocks': target_stocks
        }
    
    def _solve_qubo(self, 
                   Q: np.ndarray, 
                   linear: np.ndarray,
                   num_reads: int = 1000,
                   **solver_kwargs) -> Tuple[np.ndarray, float, Dict]:
        """
        Solve QUBO problem using OpenJij.
        
        Args:
            Q: Quadratic coefficient matrix
            linear: Linear coefficients
            num_reads: Number of optimization runs
            **solver_kwargs: Additional solver arguments
            
        Returns:
            Tuple of (solution, energy, solver_info)
        """
        n_vars = len(linear)
        
        # Create QUBO matrix (upper triangular)
        qubo_matrix = {}
        
        # Add linear terms
        for i in range(n_vars):
            if linear[i] != 0:
                qubo_matrix[(i, i)] = linear[i]
        
        # Add quadratic terms
        for i in range(n_vars):
            for j in range(i+1, n_vars):
                if Q[i, j] != 0:
                    qubo_matrix[(i, j)] = Q[i, j]
        
        # Choose solver based on type
        if self.solver_type == 'simulated_annealing':
            sampler = oj.SASampler()
            default_params = {
                'num_reads': num_reads,
                'num_sweeps': 1000,
                'beta_min': 0.1,
                'beta_max': 10.0
            }
        elif self.solver_type == 'sqa':
            sampler = oj.SQASampler()
            default_params = {
                'num_reads': num_reads,
                'num_sweeps': 1000,
                'beta': 10.0,
                'gamma': 1.0
            }
        else:
            # Default to simulated annealing
            sampler = oj.SASampler()
            default_params = {
                'num_reads': num_reads,
                'num_sweeps': 1000,
                'beta_min': 0.1,
                'beta_max': 10.0
            }
        
        # Merge with user-provided parameters
        params = {**default_params, **solver_kwargs}
        
        # Solve
        response = sampler.sample_qubo(qubo_matrix, **params)
        
        # Get best solution
        best_sample = response.first.sample
        best_energy = response.first.energy
        
        # Convert to array format
        solution = np.array([best_sample.get(i, 0) for i in range(n_vars)])
        
        # Solver info
        solver_info = {
            'solver_type': self.solver_type,
            'num_reads': len(response),
            'best_energy': best_energy,
            'parameters': params,
            'all_energies': [sample.energy for sample in response.data()],
            'energy_statistics': {
                'min': min(sample.energy for sample in response.data()),
                'max': max(sample.energy for sample in response.data()),
                'mean': np.mean([sample.energy for sample in response.data()]),
                'std': np.std([sample.energy for sample in response.data()])
            }
        }
        
        return solution, best_energy, solver_info
    
    def rebalance_portfolio(self,
                           current_portfolio: List[str],
                           universe: List[str],
                           target_stocks: int,
                           lookback_days: int = 252,
                           min_turnover: float = 0.1,
                           max_turnover: float = 0.5,
                           **kwargs) -> Dict[str, Any]:
        """
        Rebalance existing portfolio with turnover constraints.
        
        Args:
            current_portfolio: Currently held stocks
            universe: Available stock universe
            target_stocks: Target number of stocks
            lookback_days: Lookback period for returns
            min_turnover: Minimum portfolio turnover
            max_turnover: Maximum portfolio turnover
            **kwargs: Additional arguments for select_stocks
            
        Returns:
            Rebalancing results
        """
        # Get optimal portfolio without turnover constraints
        optimal_result = self.select_stocks(
            universe=universe,
            target_stocks=target_stocks,
            lookback_days=lookback_days,
            **kwargs
        )
        
        optimal_stocks = set(optimal_result['selected_stocks'])
        current_stocks = set(current_portfolio)
        
        # Calculate turnover
        stocks_to_sell = current_stocks - optimal_stocks
        stocks_to_buy = optimal_stocks - current_stocks
        
        turnover = (len(stocks_to_sell) + len(stocks_to_buy)) / (2 * target_stocks)
        
        # Apply turnover constraints
        if turnover < min_turnover:
            # Force some turnover by replacing worst performing stocks
            returns = optimal_result['returns_data']
            if not returns.empty:
                current_returns = returns[list(current_stocks & set(returns.columns))].mean()
                worst_stocks = current_returns.nsmallest(max(1, int(min_turnover * target_stocks))).index.tolist()
                
                # Replace worst stocks with best from optimal selection
                optimal_returns = returns[list(optimal_stocks)].mean()
                best_new_stocks = optimal_returns.nlargest(len(worst_stocks)).index.tolist()
                
                final_portfolio = list((current_stocks - set(worst_stocks)) | set(best_new_stocks))
            else:
                final_portfolio = list(optimal_stocks)
                
        elif turnover > max_turnover:
            # Limit turnover by keeping more current stocks
            max_changes = int(max_turnover * target_stocks)
            
            # Prioritize keeping current stocks that are also in optimal
            keep_stocks = current_stocks & optimal_stocks
            
            # Add best current stocks not in optimal
            if len(keep_stocks) < target_stocks - max_changes:
                returns = optimal_result['returns_data']
                if not returns.empty:
                    remaining_current = current_stocks - optimal_stocks
                    if remaining_current and len(remaining_current & set(returns.columns)) > 0:
                        current_returns = returns[list(remaining_current & set(returns.columns))].mean()
                        additional_keep = current_returns.nlargest(
                            target_stocks - max_changes - len(keep_stocks)
                        ).index.tolist()
                        keep_stocks.update(additional_keep)
            
            # Fill remaining slots with optimal stocks
            remaining_slots = target_stocks - len(keep_stocks)
            new_stocks = list(optimal_stocks - keep_stocks)[:remaining_slots]
            
            final_portfolio = list(keep_stocks) + new_stocks
        else:
            final_portfolio = list(optimal_stocks)
        
        # Ensure we have exactly target_stocks
        final_portfolio = final_portfolio[:target_stocks]
        
        return {
            'new_portfolio': final_portfolio,
            'stocks_to_buy': list(set(final_portfolio) - current_stocks),
            'stocks_to_sell': list(current_stocks - set(final_portfolio)),
            'turnover': (len(set(final_portfolio) - current_stocks) + 
                        len(current_stocks - set(final_portfolio))) / (2 * target_stocks),
            'optimal_result': optimal_result,
            'current_portfolio': current_portfolio
        }
