"""
Risk Manager Module

This module implements risk management functionality for the trading system.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from scipy import stats
import warnings
warnings.filterwarnings('ignore')


class RiskManager:
    """
    Risk management system for portfolio monitoring and control.
    """
    
    def __init__(self,
                 max_portfolio_volatility: float = 0.20,
                 max_individual_weight: float = 0.15,
                 max_sector_weight: float = 0.40,
                 max_drawdown_threshold: float = 0.10,
                 var_confidence: float = 0.05,
                 lookback_days: int = 252):
        """
        Initialize risk manager.
        
        Args:
            max_portfolio_volatility: Maximum allowed portfolio volatility (annualized)
            max_individual_weight: Maximum weight for individual stock
            max_sector_weight: Maximum weight for any sector
            max_drawdown_threshold: Maximum allowed drawdown
            var_confidence: Confidence level for VaR calculation
            lookback_days: Days of data for risk calculations
        """
        self.max_portfolio_volatility = max_portfolio_volatility
        self.max_individual_weight = max_individual_weight
        self.max_sector_weight = max_sector_weight
        self.max_drawdown_threshold = max_drawdown_threshold
        self.var_confidence = var_confidence
        self.lookback_days = lookback_days
        
    def assess_portfolio_risk(self,
                             portfolio: List[str],
                             prices: pd.DataFrame,
                             weights: Optional[Dict[str, float]] = None,
                             sector_info: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Assess portfolio risk metrics.
        
        Args:
            portfolio: List of stocks in portfolio
            prices: Price data DataFrame
            weights: Portfolio weights (if None, assumes equal weight)
            sector_info: Sector information for stocks
            
        Returns:
            Dictionary with risk assessment results
        """
        if not portfolio or prices.empty:
            return {'risk_level': 'UNKNOWN', 'metrics': {}}
        
        # Filter prices for portfolio stocks
        portfolio_prices = prices[[stock for stock in portfolio if stock in prices.columns]]
        
        if portfolio_prices.empty:
            return {'risk_level': 'UNKNOWN', 'metrics': {}}
        
        # Calculate returns
        returns = portfolio_prices.pct_change().dropna()
        
        if returns.empty:
            return {'risk_level': 'UNKNOWN', 'metrics': {}}
        
        # Set equal weights if not provided
        if weights is None:
            weights = {stock: 1.0/len(portfolio) for stock in portfolio if stock in returns.columns}
        
        # Calculate portfolio metrics
        portfolio_metrics = self._calculate_portfolio_metrics(returns, weights)
        
        # Calculate individual stock metrics
        individual_metrics = self._calculate_individual_metrics(returns, weights)
        
        # Calculate sector concentration if sector info provided
        sector_metrics = {}
        if sector_info:
            sector_metrics = self._calculate_sector_metrics(portfolio, weights, sector_info)
        
        # Risk assessment
        risk_flags = self._assess_risk_flags(portfolio_metrics, individual_metrics, sector_metrics)
        
        # Overall risk level
        risk_level = self._determine_risk_level(risk_flags)
        
        return {
            'risk_level': risk_level,
            'risk_flags': risk_flags,
            'portfolio_metrics': portfolio_metrics,
            'individual_metrics': individual_metrics,
            'sector_metrics': sector_metrics,
            'recommendations': self._generate_recommendations(risk_flags, portfolio_metrics)
        }
    
    def _calculate_portfolio_metrics(self,
                                   returns: pd.DataFrame,
                                   weights: Dict[str, float]) -> Dict[str, float]:
        """Calculate portfolio-level risk metrics."""
        # Portfolio returns
        weight_series = pd.Series(weights)
        aligned_weights = weight_series.reindex(returns.columns, fill_value=0)
        aligned_weights = aligned_weights / aligned_weights.sum()  # Normalize
        
        portfolio_returns = (returns * aligned_weights).sum(axis=1)
        
        # Basic metrics
        volatility = portfolio_returns.std() * np.sqrt(252)  # Annualized
        mean_return = portfolio_returns.mean() * 252  # Annualized
        
        # VaR and CVaR
        var_95 = np.percentile(portfolio_returns, self.var_confidence * 100)
        cvar_95 = portfolio_returns[portfolio_returns <= var_95].mean()
        
        # Maximum drawdown
        cumulative = (1 + portfolio_returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Sharpe ratio
        sharpe_ratio = mean_return / volatility if volatility > 0 else 0
        
        # Correlation with market (using equal-weighted portfolio as proxy)
        market_proxy = returns.mean(axis=1)
        correlation_with_market = portfolio_returns.corr(market_proxy)
        
        # Beta calculation
        covariance = np.cov(portfolio_returns, market_proxy)[0, 1]
        market_variance = market_proxy.var()
        beta = covariance / market_variance if market_variance > 0 else 1.0
        
        return {
            'volatility': volatility,
            'annualized_return': mean_return,
            'sharpe_ratio': sharpe_ratio,
            'var_95': var_95,
            'cvar_95': cvar_95,
            'max_drawdown': max_drawdown,
            'beta': beta,
            'correlation_with_market': correlation_with_market,
            'tracking_error': (portfolio_returns - market_proxy).std() * np.sqrt(252)
        }
    
    def _calculate_individual_metrics(self,
                                    returns: pd.DataFrame,
                                    weights: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """Calculate individual stock risk metrics."""
        individual_metrics = {}
        
        for stock in returns.columns:
            if stock in weights:
                stock_returns = returns[stock].dropna()
                
                if len(stock_returns) > 0:
                    individual_metrics[stock] = {
                        'weight': weights[stock],
                        'volatility': stock_returns.std() * np.sqrt(252),
                        'var_95': np.percentile(stock_returns, self.var_confidence * 100),
                        'skewness': stats.skew(stock_returns),
                        'kurtosis': stats.kurtosis(stock_returns),
                        'max_daily_loss': stock_returns.min(),
                        'contribution_to_risk': weights[stock] * stock_returns.std() * np.sqrt(252)
                    }
        
        return individual_metrics
    
    def _calculate_sector_metrics(self,
                                portfolio: List[str],
                                weights: Dict[str, float],
                                sector_info: Dict[str, str]) -> Dict[str, float]:
        """Calculate sector concentration metrics."""
        sector_weights = {}
        
        for stock in portfolio:
            if stock in weights and stock in sector_info:
                sector = sector_info[stock]
                sector_weights[sector] = sector_weights.get(sector, 0) + weights[stock]
        
        return {
            'sector_weights': sector_weights,
            'max_sector_weight': max(sector_weights.values()) if sector_weights else 0,
            'sector_concentration': len(sector_weights),
            'herfindahl_index': sum(w**2 for w in sector_weights.values()) if sector_weights else 0
        }
    
    def _assess_risk_flags(self,
                          portfolio_metrics: Dict[str, float],
                          individual_metrics: Dict[str, Dict[str, float]],
                          sector_metrics: Dict[str, float]) -> Dict[str, bool]:
        """Assess various risk flags."""
        flags = {}
        
        # Portfolio level flags
        flags['high_volatility'] = portfolio_metrics.get('volatility', 0) > self.max_portfolio_volatility
        flags['high_drawdown'] = abs(portfolio_metrics.get('max_drawdown', 0)) > self.max_drawdown_threshold
        flags['negative_sharpe'] = portfolio_metrics.get('sharpe_ratio', 0) < 0
        flags['high_beta'] = abs(portfolio_metrics.get('beta', 1)) > 1.5
        
        # Individual stock flags
        flags['concentration_risk'] = any(
            metrics['weight'] > self.max_individual_weight 
            for metrics in individual_metrics.values()
        )
        
        flags['high_individual_volatility'] = any(
            metrics['volatility'] > 0.5  # 50% annualized volatility threshold
            for metrics in individual_metrics.values()
        )
        
        # Sector concentration flags
        if sector_metrics:
            flags['sector_concentration'] = sector_metrics.get('max_sector_weight', 0) > self.max_sector_weight
            flags['low_diversification'] = sector_metrics.get('sector_concentration', 0) < 3
        else:
            flags['sector_concentration'] = False
            flags['low_diversification'] = False
        
        return flags
    
    def _determine_risk_level(self, risk_flags: Dict[str, bool]) -> str:
        """Determine overall risk level based on flags."""
        high_risk_flags = [
            'high_volatility', 'high_drawdown', 'concentration_risk', 
            'sector_concentration', 'high_individual_volatility'
        ]
        
        medium_risk_flags = [
            'negative_sharpe', 'high_beta', 'low_diversification'
        ]
        
        high_risk_count = sum(risk_flags.get(flag, False) for flag in high_risk_flags)
        medium_risk_count = sum(risk_flags.get(flag, False) for flag in medium_risk_flags)
        
        if high_risk_count >= 2:
            return 'HIGH'
        elif high_risk_count >= 1 or medium_risk_count >= 2:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _generate_recommendations(self,
                                risk_flags: Dict[str, bool],
                                portfolio_metrics: Dict[str, float]) -> List[str]:
        """Generate risk management recommendations."""
        recommendations = []
        
        if risk_flags.get('high_volatility', False):
            recommendations.append("Reduce portfolio volatility by diversifying or reducing position sizes")
        
        if risk_flags.get('high_drawdown', False):
            recommendations.append("Implement stop-loss mechanisms to limit drawdowns")
        
        if risk_flags.get('concentration_risk', False):
            recommendations.append("Reduce individual stock weights to improve diversification")
        
        if risk_flags.get('sector_concentration', False):
            recommendations.append("Diversify across more sectors to reduce concentration risk")
        
        if risk_flags.get('negative_sharpe', False):
            recommendations.append("Review strategy performance and consider risk-adjusted optimization")
        
        if risk_flags.get('high_beta', False):
            recommendations.append("Consider adding defensive stocks to reduce market sensitivity")
        
        if risk_flags.get('low_diversification', False):
            recommendations.append("Increase the number of sectors in the portfolio")
        
        if not recommendations:
            recommendations.append("Portfolio risk levels are within acceptable limits")
        
        return recommendations
    
    def calculate_position_sizes(self,
                               expected_returns: pd.Series,
                               covariance_matrix: pd.DataFrame,
                               risk_budget: float = 0.15) -> Dict[str, float]:
        """
        Calculate optimal position sizes based on risk budgeting.
        
        Args:
            expected_returns: Expected returns for stocks
            covariance_matrix: Covariance matrix of returns
            risk_budget: Target portfolio volatility
            
        Returns:
            Dictionary with optimal weights
        """
        try:
            # Risk parity approach
            n_assets = len(expected_returns)
            
            # Equal risk contribution weights as starting point
            weights = np.ones(n_assets) / n_assets
            
            # Iterative optimization for risk parity
            for _ in range(100):  # Max iterations
                portfolio_vol = np.sqrt(weights.T @ covariance_matrix @ weights)
                marginal_contrib = covariance_matrix @ weights / portfolio_vol
                contrib = weights * marginal_contrib
                
                # Target equal risk contribution
                target_contrib = portfolio_vol / n_assets
                
                # Adjust weights
                weights = weights * target_contrib / contrib
                weights = weights / weights.sum()  # Normalize
                
                # Check convergence
                if np.max(np.abs(contrib - target_contrib)) < 1e-6:
                    break
            
            # Scale to target risk budget
            current_vol = np.sqrt(weights.T @ covariance_matrix @ weights)
            scale_factor = risk_budget / current_vol
            weights = weights * scale_factor
            
            # Ensure weights sum to 1 and apply constraints
            weights = np.clip(weights, 0, self.max_individual_weight)
            weights = weights / weights.sum()
            
            return dict(zip(expected_returns.index, weights))
            
        except Exception as e:
            # Fallback to equal weights
            n_assets = len(expected_returns)
            equal_weight = 1.0 / n_assets
            return {stock: equal_weight for stock in expected_returns.index}
