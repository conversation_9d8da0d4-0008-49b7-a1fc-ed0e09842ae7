"""
Data Provider Module

This module provides interfaces for fetching market data from various sources.
"""

import pandas as pd
import numpy as np
import yfinance as yf
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class BaseDataProvider:
    """Base class for data providers."""
    
    def get_returns(self, 
                   symbols: List[str], 
                   start_date: str, 
                   end_date: str) -> pd.DataFrame:
        """Get stock returns for given symbols and date range."""
        raise NotImplementedError
    
    def get_prices(self, 
                  symbols: List[str], 
                  start_date: str, 
                  end_date: str) -> pd.DataFrame:
        """Get stock prices for given symbols and date range."""
        raise NotImplementedError
    
    def get_sector_info(self, symbols: List[str]) -> Dict[str, str]:
        """Get sector information for given symbols."""
        raise NotImplementedError


class YahooDataProvider(BaseDataProvider):
    """Yahoo Finance data provider."""
    
    def __init__(self):
        self.cache = {}
        
    def get_prices(self, 
                  symbols: List[str], 
                  start_date: str, 
                  end_date: str,
                  price_type: str = 'Adj Close') -> pd.DataFrame:
        """
        Get stock prices from Yahoo Finance.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            price_type: Type of price ('Open', 'High', 'Low', 'Close', 'Adj Close')
            
        Returns:
            DataFrame with prices (dates as index, symbols as columns)
        """
        cache_key = f"{'-'.join(symbols)}_{start_date}_{end_date}_{price_type}"
        
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            # Download data for all symbols
            data = yf.download(symbols, start=start_date, end=end_date, progress=False)
            
            if len(symbols) == 1:
                # Single symbol case
                prices = data[price_type].to_frame()
                prices.columns = symbols
            else:
                # Multiple symbols case
                prices = data[price_type]
                
            # Remove any columns with all NaN values
            prices = prices.dropna(axis=1, how='all')
            
            # Forward fill missing values
            prices = prices.fillna(method='ffill').dropna()
            
            self.cache[cache_key] = prices
            return prices
            
        except Exception as e:
            print(f"Error fetching data for {symbols}: {e}")
            return pd.DataFrame()
    
    def get_returns(self, 
                   symbols: List[str], 
                   start_date: str, 
                   end_date: str,
                   return_type: str = 'simple') -> pd.DataFrame:
        """
        Get stock returns.
        
        Args:
            symbols: List of stock symbols
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            return_type: 'simple' or 'log' returns
            
        Returns:
            DataFrame with returns (dates as index, symbols as columns)
        """
        prices = self.get_prices(symbols, start_date, end_date)
        
        if prices.empty:
            return pd.DataFrame()
        
        if return_type == 'simple':
            returns = prices.pct_change().dropna()
        elif return_type == 'log':
            returns = np.log(prices / prices.shift(1)).dropna()
        else:
            raise ValueError("return_type must be 'simple' or 'log'")
            
        return returns
    
    def get_sector_info(self, symbols: List[str]) -> Dict[str, str]:
        """
        Get sector information for symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to sectors
        """
        sector_info = {}
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                sector = info.get('sector', 'Unknown')
                sector_info[symbol] = sector
            except Exception as e:
                print(f"Could not get sector info for {symbol}: {e}")
                sector_info[symbol] = 'Unknown'
                
        return sector_info
    
    def get_market_cap(self, symbols: List[str]) -> Dict[str, float]:
        """Get market capitalization for symbols."""
        market_caps = {}

        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                market_cap = info.get('marketCap', 0)
                market_caps[symbol] = market_cap
            except Exception as e:
                print(f"Could not get market cap for {symbol}: {e}")
                market_caps[symbol] = 0

        return market_caps

    def get_intraday_data(self,
                         symbols: List[str],
                         period: str = '1d',
                         interval: str = '1m') -> Dict[str, pd.DataFrame]:
        """
        Get intraday data with OHLCV for VWAP calculations.

        Args:
            symbols: List of stock symbols
            period: Data period ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
            interval: Data interval ('1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo')

        Returns:
            Dictionary mapping symbols to OHLCV DataFrames
        """
        intraday_data = {}

        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                data = ticker.history(period=period, interval=interval)

                if not data.empty:
                    # Ensure we have the required columns for VWAP
                    required_cols = ['High', 'Low', 'Close', 'Volume']
                    if all(col in data.columns for col in required_cols):
                        intraday_data[symbol] = data
                    else:
                        print(f"Missing required columns for {symbol}")

            except Exception as e:
                print(f"Could not get intraday data for {symbol}: {e}")

        return intraday_data


class SyntheticDataProvider(BaseDataProvider):
    """Synthetic data provider for testing."""
    
    def __init__(self, n_stocks: int = 10, seed: int = 42):
        """
        Initialize synthetic data provider.
        
        Args:
            n_stocks: Number of stocks to generate
            seed: Random seed for reproducibility
        """
        self.n_stocks = n_stocks
        self.seed = seed
        np.random.seed(seed)
        
        # Generate stock symbols
        self.symbols = [f"STOCK_{i:02d}" for i in range(n_stocks)]
        
        # Generate sector information
        sectors = ['Technology', 'Healthcare', 'Finance', 'Energy', 'Consumer']
        self.sector_map = {
            symbol: sectors[i % len(sectors)] 
            for i, symbol in enumerate(self.symbols)
        }
        
    def get_prices(self, 
                  symbols: List[str], 
                  start_date: str, 
                  end_date: str) -> pd.DataFrame:
        """Generate synthetic price data."""
        # Create date range
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # Only weekdays
        
        n_days = len(dates)
        n_symbols = len(symbols)
        
        # Generate correlated returns
        np.random.seed(self.seed)
        
        # Create correlation matrix
        correlation = np.random.uniform(0.1, 0.7, (n_symbols, n_symbols))
        correlation = (correlation + correlation.T) / 2
        np.fill_diagonal(correlation, 1.0)
        
        # Generate returns using multivariate normal
        mean_returns = np.random.uniform(-0.001, 0.002, n_symbols)
        volatilities = np.random.uniform(0.01, 0.03, n_symbols)
        
        # Create covariance matrix
        cov_matrix = np.outer(volatilities, volatilities) * correlation
        
        # Generate returns
        returns = np.random.multivariate_normal(mean_returns, cov_matrix, n_days)
        
        # Convert to prices (starting at 100)
        initial_prices = np.full(n_symbols, 100.0)
        prices = np.zeros((n_days, n_symbols))
        prices[0] = initial_prices
        
        for i in range(1, n_days):
            prices[i] = prices[i-1] * (1 + returns[i])
            
        # Create DataFrame
        price_df = pd.DataFrame(prices, index=dates, columns=symbols)
        
        return price_df
    
    def get_returns(self, 
                   symbols: List[str], 
                   start_date: str, 
                   end_date: str,
                   return_type: str = 'simple') -> pd.DataFrame:
        """Get synthetic returns."""
        prices = self.get_prices(symbols, start_date, end_date)
        
        if return_type == 'simple':
            returns = prices.pct_change().dropna()
        elif return_type == 'log':
            returns = np.log(prices / prices.shift(1)).dropna()
        else:
            raise ValueError("return_type must be 'simple' or 'log'")
            
        return returns
    
    def get_sector_info(self, symbols: List[str]) -> Dict[str, str]:
        """Get synthetic sector information."""
        return {symbol: self.sector_map.get(symbol, 'Unknown') for symbol in symbols}


def create_sample_data(n_stocks: int = 20, 
                      n_days: int = 252,
                      start_date: str = "2023-01-01") -> Tuple[pd.DataFrame, Dict[str, str]]:
    """
    Create sample data for testing.
    
    Args:
        n_stocks: Number of stocks
        n_days: Number of trading days
        start_date: Start date
        
    Returns:
        Tuple of (returns DataFrame, sector info dict)
    """
    provider = SyntheticDataProvider(n_stocks=n_stocks)
    
    end_date = (pd.to_datetime(start_date) + timedelta(days=int(n_days * 1.4))).strftime('%Y-%m-%d')
    
    symbols = provider.symbols[:n_stocks]
    returns = provider.get_returns(symbols, start_date, end_date)
    sector_info = provider.get_sector_info(symbols)
    
    return returns, sector_info
