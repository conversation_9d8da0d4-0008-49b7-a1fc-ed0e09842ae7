"""
Utility Functions

This module contains utility functions for the stock optimization system.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')


def calculate_portfolio_performance(returns: pd.DataFrame,
                                  weights: Dict[str, float],
                                  benchmark_returns: Optional[pd.Series] = None) -> Dict[str, float]:
    """
    Calculate comprehensive portfolio performance metrics.
    
    Args:
        returns: DataFrame with stock returns
        weights: Portfolio weights
        benchmark_returns: Benchmark returns for comparison
        
    Returns:
        Dictionary with performance metrics
    """
    # Align weights with returns
    weight_series = pd.Series(weights)
    aligned_weights = weight_series.reindex(returns.columns, fill_value=0)
    aligned_weights = aligned_weights / aligned_weights.sum()
    
    # Portfolio returns
    portfolio_returns = (returns * aligned_weights).sum(axis=1)
    
    # Basic metrics
    total_return = (1 + portfolio_returns).prod() - 1
    annualized_return = (1 + total_return) ** (252 / len(portfolio_returns)) - 1
    volatility = portfolio_returns.std() * np.sqrt(252)
    
    # Risk-adjusted metrics
    sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
    
    # Drawdown analysis
    cumulative = (1 + portfolio_returns).cumprod()
    running_max = cumulative.expanding().max()
    drawdown = (cumulative - running_max) / running_max
    max_drawdown = drawdown.min()
    
    # Calmar ratio
    calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
    
    metrics = {
        'total_return': total_return,
        'annualized_return': annualized_return,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'calmar_ratio': calmar_ratio,
        'skewness': portfolio_returns.skew(),
        'kurtosis': portfolio_returns.kurtosis()
    }
    
    # Benchmark comparison if provided
    if benchmark_returns is not None:
        benchmark_aligned = benchmark_returns.reindex(portfolio_returns.index).fillna(0)
        
        # Tracking error
        tracking_error = (portfolio_returns - benchmark_aligned).std() * np.sqrt(252)
        
        # Information ratio
        excess_return = portfolio_returns.mean() - benchmark_aligned.mean()
        information_ratio = (excess_return * 252) / tracking_error if tracking_error > 0 else 0
        
        # Beta
        covariance = np.cov(portfolio_returns, benchmark_aligned)[0, 1]
        benchmark_variance = benchmark_aligned.var()
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 1.0
        
        # Alpha
        risk_free_rate = 0.02  # Assume 2% risk-free rate
        alpha = annualized_return - (risk_free_rate + beta * (benchmark_aligned.mean() * 252 - risk_free_rate))
        
        metrics.update({
            'tracking_error': tracking_error,
            'information_ratio': information_ratio,
            'beta': beta,
            'alpha': alpha
        })
    
    return metrics


def plot_portfolio_analysis(returns: pd.DataFrame,
                          weights: Dict[str, float],
                          selected_stocks: List[str],
                          save_path: Optional[str] = None) -> None:
    """
    Create comprehensive portfolio analysis plots.
    
    Args:
        returns: DataFrame with stock returns
        weights: Portfolio weights
        selected_stocks: List of selected stocks
        save_path: Path to save the plot
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Portfolio Analysis Dashboard', fontsize=16, fontweight='bold')
    
    # Portfolio returns
    weight_series = pd.Series(weights)
    aligned_weights = weight_series.reindex(returns.columns, fill_value=0)
    aligned_weights = aligned_weights / aligned_weights.sum()
    portfolio_returns = (returns * aligned_weights).sum(axis=1)
    
    # 1. Cumulative returns
    cumulative = (1 + portfolio_returns).cumprod()
    axes[0, 0].plot(cumulative.index, cumulative.values, linewidth=2, color='blue')
    axes[0, 0].set_title('Cumulative Portfolio Returns')
    axes[0, 0].set_ylabel('Cumulative Return')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Portfolio weights
    weights_to_plot = {k: v for k, v in weights.items() if k in selected_stocks}
    if weights_to_plot:
        axes[0, 1].pie(weights_to_plot.values(), labels=weights_to_plot.keys(), autopct='%1.1f%%')
        axes[0, 1].set_title('Portfolio Weights')
    
    # 3. Return distribution
    axes[0, 2].hist(portfolio_returns, bins=50, alpha=0.7, color='green', edgecolor='black')
    axes[0, 2].axvline(portfolio_returns.mean(), color='red', linestyle='--', 
                       label=f'Mean: {portfolio_returns.mean():.4f}')
    axes[0, 2].set_title('Return Distribution')
    axes[0, 2].set_xlabel('Daily Returns')
    axes[0, 2].set_ylabel('Frequency')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. Drawdown
    running_max = cumulative.expanding().max()
    drawdown = (cumulative - running_max) / running_max
    axes[1, 0].fill_between(drawdown.index, drawdown.values, 0, alpha=0.7, color='red')
    axes[1, 0].set_title('Portfolio Drawdown')
    axes[1, 0].set_ylabel('Drawdown')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. Correlation heatmap
    if len(selected_stocks) > 1:
        selected_returns = returns[selected_stocks]
        correlation_matrix = selected_returns.corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   ax=axes[1, 1], square=True, fmt='.2f')
        axes[1, 1].set_title('Stock Correlation Matrix')
    else:
        axes[1, 1].text(0.5, 0.5, 'Insufficient stocks\nfor correlation analysis', 
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('Stock Correlation Matrix')
    
    # 6. Risk-return scatter
    if len(selected_stocks) > 1:
        stock_returns = returns[selected_stocks].mean() * 252
        stock_volatility = returns[selected_stocks].std() * np.sqrt(252)
        
        scatter = axes[1, 2].scatter(stock_volatility, stock_returns, 
                                   s=[weights.get(stock, 0) * 1000 for stock in selected_stocks],
                                   alpha=0.7, c=range(len(selected_stocks)), cmap='viridis')
        
        for i, stock in enumerate(selected_stocks):
            axes[1, 2].annotate(stock, (stock_volatility[stock], stock_returns[stock]),
                              xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        axes[1, 2].set_xlabel('Volatility (Annualized)')
        axes[1, 2].set_ylabel('Expected Return (Annualized)')
        axes[1, 2].set_title('Risk-Return Profile')
        axes[1, 2].grid(True, alpha=0.3)
    else:
        axes[1, 2].text(0.5, 0.5, 'Insufficient stocks\nfor risk-return analysis', 
                       ha='center', va='center', transform=axes[1, 2].transAxes)
        axes[1, 2].set_title('Risk-Return Profile')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def create_optimization_report(optimization_result: Dict[str, Any],
                             save_path: Optional[str] = None) -> str:
    """
    Create a detailed optimization report.
    
    Args:
        optimization_result: Result from stock optimization
        save_path: Path to save the report
        
    Returns:
        Report as string
    """
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("STOCK OPTIMIZATION REPORT")
    report_lines.append("=" * 80)
    report_lines.append("")
    
    # Basic information
    report_lines.append("PORTFOLIO SUMMARY")
    report_lines.append("-" * 40)
    report_lines.append(f"Selected Stocks: {len(optimization_result['selected_stocks'])}")
    report_lines.append(f"Target Stocks: {optimization_result['target_stocks']}")
    report_lines.append(f"Universe Size: {optimization_result['universe_size']}")
    report_lines.append("")
    
    # Selected stocks
    report_lines.append("SELECTED STOCKS")
    report_lines.append("-" * 40)
    for i, stock in enumerate(optimization_result['selected_stocks'], 1):
        report_lines.append(f"{i:2d}. {stock}")
    report_lines.append("")
    
    # Objective function components
    obj_comp = optimization_result['objective_components']
    report_lines.append("OBJECTIVE FUNCTION ANALYSIS")
    report_lines.append("-" * 40)
    report_lines.append(f"Total Objective Value: {obj_comp['total_objective']:.6f}")
    report_lines.append(f"Expected Return: {obj_comp['expected_return']:.4f}")
    report_lines.append(f"Average Correlation: {obj_comp['avg_correlation']:.4f}")
    report_lines.append(f"Profitability Component: {obj_comp['profitability']:.6f}")
    report_lines.append(f"Correlation Penalty: {obj_comp['correlation_penalty']:.6f}")
    report_lines.append(f"Balance Penalty: {obj_comp['balance_penalty']:.6f}")
    report_lines.append("")
    
    # Validation results
    validation = optimization_result['validation']
    report_lines.append("CONSTRAINT VALIDATION")
    report_lines.append("-" * 40)
    report_lines.append(f"Number of Stocks OK: {validation['num_stocks_ok']}")
    report_lines.append(f"Selected Count: {validation['selected_count']}")
    report_lines.append(f"Sector Constraints OK: {validation['sector_constraints_ok']}")
    
    if validation['sector_distribution']:
        report_lines.append("\nSector Distribution:")
        for sector, count in validation['sector_distribution'].items():
            report_lines.append(f"  {sector}: {count} stocks")
    report_lines.append("")
    
    # Solver information
    solver_info = optimization_result['solver_info']
    report_lines.append("SOLVER INFORMATION")
    report_lines.append("-" * 40)
    report_lines.append(f"Solver Type: {solver_info['solver_type']}")
    report_lines.append(f"Number of Reads: {solver_info['num_reads']}")
    report_lines.append(f"Best Energy: {solver_info['best_energy']:.6f}")
    
    energy_stats = solver_info['energy_statistics']
    report_lines.append(f"Energy Statistics:")
    report_lines.append(f"  Min: {energy_stats['min']:.6f}")
    report_lines.append(f"  Max: {energy_stats['max']:.6f}")
    report_lines.append(f"  Mean: {energy_stats['mean']:.6f}")
    report_lines.append(f"  Std: {energy_stats['std']:.6f}")
    report_lines.append("")
    
    # Performance metrics (if returns data available)
    if not optimization_result['returns_data'].empty:
        returns_data = optimization_result['returns_data']
        weights = {stock: 1.0/len(optimization_result['selected_stocks']) 
                  for stock in optimization_result['selected_stocks']}
        
        performance = calculate_portfolio_performance(returns_data, weights)
        
        report_lines.append("PERFORMANCE METRICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Annualized Return: {performance['annualized_return']:.2%}")
        report_lines.append(f"Volatility: {performance['volatility']:.2%}")
        report_lines.append(f"Sharpe Ratio: {performance['sharpe_ratio']:.4f}")
        report_lines.append(f"Maximum Drawdown: {performance['max_drawdown']:.2%}")
        report_lines.append(f"Calmar Ratio: {performance['calmar_ratio']:.4f}")
        report_lines.append("")
    
    report_lines.append("=" * 80)
    
    report_text = "\n".join(report_lines)
    
    if save_path:
        with open(save_path, 'w') as f:
            f.write(report_text)
    
    return report_text


def validate_data_quality(returns: pd.DataFrame,
                         min_observations: int = 30,
                         max_missing_pct: float = 0.1) -> Dict[str, Any]:
    """
    Validate data quality for optimization.
    
    Args:
        returns: Returns DataFrame
        min_observations: Minimum number of observations required
        max_missing_pct: Maximum percentage of missing values allowed
        
    Returns:
        Data quality report
    """
    quality_report = {
        'total_stocks': len(returns.columns),
        'total_observations': len(returns),
        'valid_stocks': [],
        'invalid_stocks': [],
        'quality_issues': []
    }
    
    for stock in returns.columns:
        stock_data = returns[stock]
        
        # Check number of observations
        valid_obs = stock_data.count()
        missing_pct = (len(stock_data) - valid_obs) / len(stock_data)
        
        issues = []
        
        if valid_obs < min_observations:
            issues.append(f"Insufficient observations: {valid_obs} < {min_observations}")
        
        if missing_pct > max_missing_pct:
            issues.append(f"Too many missing values: {missing_pct:.1%} > {max_missing_pct:.1%}")
        
        # Check for extreme values
        if valid_obs > 0:
            stock_clean = stock_data.dropna()
            if len(stock_clean) > 0:
                q99 = stock_clean.quantile(0.99)
                q01 = stock_clean.quantile(0.01)
                
                if q99 > 0.5 or q01 < -0.5:  # 50% daily return threshold
                    issues.append("Extreme returns detected")
                
                # Check for constant values
                if stock_clean.std() < 1e-8:
                    issues.append("Constant or near-constant values")
        
        if issues:
            quality_report['invalid_stocks'].append({
                'stock': stock,
                'issues': issues,
                'valid_observations': valid_obs,
                'missing_percentage': missing_pct
            })
        else:
            quality_report['valid_stocks'].append(stock)
    
    # Overall quality assessment
    valid_ratio = len(quality_report['valid_stocks']) / quality_report['total_stocks']
    
    if valid_ratio >= 0.8:
        quality_report['overall_quality'] = 'GOOD'
    elif valid_ratio >= 0.6:
        quality_report['overall_quality'] = 'FAIR'
    else:
        quality_report['overall_quality'] = 'POOR'
    
    return quality_report


def export_results_to_csv(optimization_result: Dict[str, Any],
                         file_path: str) -> None:
    """
    Export optimization results to CSV format.
    
    Args:
        optimization_result: Optimization results
        file_path: Path to save CSV file
    """
    # Create results DataFrame
    results_data = []
    
    for stock in optimization_result['selected_stocks']:
        row = {
            'Stock': stock,
            'Selected': 1,
            'Weight': 1.0 / len(optimization_result['selected_stocks'])
        }
        
        # Add sector information if available
        sector_dist = optimization_result.get('sector_distribution', {})
        for sector, count in sector_dist.items():
            if stock in optimization_result['selected_stocks']:
                # This is a simplified assignment - in practice, you'd need sector mapping
                row['Sector'] = sector
                break
        
        results_data.append(row)
    
    # Add performance metrics
    obj_comp = optimization_result['objective_components']
    for stock_data in results_data:
        stock_data.update({
            'Expected_Return': obj_comp['expected_return'] / len(optimization_result['selected_stocks']),
            'Avg_Correlation': obj_comp['avg_correlation']
        })
    
    df = pd.DataFrame(results_data)
    df.to_csv(file_path, index=False)
    print(f"Results exported to {file_path}")
