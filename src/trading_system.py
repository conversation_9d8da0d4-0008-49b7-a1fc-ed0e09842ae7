"""
Real-Time Trading System

This module implements the real-time trading system that uses the stock optimizer
for continuous portfolio management.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
import time
import threading
import logging
from .stock_optimizer import StockOptimizer
from .data_provider import BaseDataProvider
from .risk_manager import RiskManager


class RealTimeTradingSystem:
    """
    Real-time trading system for automated stock selection and portfolio management.
    """
    
    def __init__(self,
                 data_provider: BaseDataProvider,
                 optimizer: StockOptimizer,
                 risk_manager: Optional[RiskManager] = None,
                 universe: List[str] = None,
                 target_stocks: int = 10,
                 rebalance_frequency: str = 'daily',
                 lookback_days: int = 252,
                 max_turnover: float = 0.3,
                 min_turnover: float = 0.05):
        """
        Initialize the trading system.
        
        Args:
            data_provider: Market data provider
            optimizer: Stock optimizer instance
            risk_manager: Risk management system
            universe: Stock universe to select from
            target_stocks: Number of stocks to hold
            rebalance_frequency: 'daily', 'weekly', 'monthly'
            lookback_days: Days of historical data for optimization
            max_turnover: Maximum portfolio turnover per rebalance
            min_turnover: Minimum portfolio turnover per rebalance
        """
        self.data_provider = data_provider
        self.optimizer = optimizer
        self.risk_manager = risk_manager or RiskManager()
        self.universe = universe or self._get_default_universe()
        self.target_stocks = target_stocks
        self.rebalance_frequency = rebalance_frequency
        self.lookback_days = lookback_days
        self.max_turnover = max_turnover
        self.min_turnover = min_turnover
        
        # Trading state
        self.current_portfolio = []
        self.portfolio_weights = {}
        self.is_running = False
        self.last_rebalance = None
        self.trading_history = []
        self.performance_metrics = {}
        
        # Callbacks
        self.on_rebalance_callback = None
        self.on_trade_callback = None
        self.on_error_callback = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def _get_default_universe(self) -> List[str]:
        """Get default stock universe (S&P 500 subset)."""
        return [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'BRK-B',
            'UNH', 'JNJ', 'JPM', 'V', 'PG', 'HD', 'MA', 'DIS', 'PYPL', 'BAC',
            'ADBE', 'CRM', 'NFLX', 'CMCSA', 'XOM', 'KO', 'ABT', 'PFE', 'TMO',
            'COST', 'AVGO', 'ACN', 'DHR', 'NEE', 'LIN', 'TXN', 'WMT', 'BMY',
            'PM', 'QCOM', 'HON', 'UPS', 'LOW', 'ORCL', 'IBM', 'MDT', 'AMGN',
            'CVX', 'SBUX', 'AMT', 'LMT', 'GE', 'CAT', 'DE', 'MMM', 'AXP'
        ]
    
    def start(self, initial_portfolio: Optional[List[str]] = None):
        """
        Start the real-time trading system.
        
        Args:
            initial_portfolio: Initial portfolio holdings
        """
        self.logger.info("Starting real-time trading system...")
        
        if initial_portfolio:
            self.current_portfolio = initial_portfolio[:self.target_stocks]
            self.portfolio_weights = {stock: 1.0/len(self.current_portfolio) 
                                    for stock in self.current_portfolio}
        
        self.is_running = True
        self.last_rebalance = datetime.now()
        
        # Start main trading loop in separate thread
        self.trading_thread = threading.Thread(target=self._trading_loop, daemon=True)
        self.trading_thread.start()
        
        self.logger.info("Trading system started successfully")
    
    def stop(self):
        """Stop the trading system."""
        self.logger.info("Stopping trading system...")
        self.is_running = False
        if hasattr(self, 'trading_thread'):
            self.trading_thread.join(timeout=5)
        self.logger.info("Trading system stopped")
    
    def _trading_loop(self):
        """Main trading loop."""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Check if rebalancing is needed
                if self._should_rebalance(current_time):
                    self._perform_rebalance()
                    
                # Update performance metrics
                self._update_performance_metrics()
                
                # Sleep for a short interval
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {e}")
                if self.on_error_callback:
                    self.on_error_callback(e)
                time.sleep(300)  # Wait 5 minutes before retrying
    
    def _should_rebalance(self, current_time: datetime) -> bool:
        """Check if portfolio should be rebalanced."""
        if self.last_rebalance is None:
            return True
            
        time_since_rebalance = current_time - self.last_rebalance
        
        if self.rebalance_frequency == 'daily':
            return time_since_rebalance >= timedelta(days=1)
        elif self.rebalance_frequency == 'weekly':
            return time_since_rebalance >= timedelta(weeks=1)
        elif self.rebalance_frequency == 'monthly':
            return time_since_rebalance >= timedelta(days=30)
        else:
            return False
    
    def _perform_rebalance(self):
        """Perform portfolio rebalancing."""
        try:
            self.logger.info("Starting portfolio rebalance...")
            
            # Get current market data for risk assessment
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            current_prices = self.data_provider.get_prices(
                self.universe, start_date, end_date
            )
            
            if current_prices.empty:
                self.logger.warning("No current price data available, skipping rebalance")
                return
            
            # Risk assessment
            risk_assessment = self.risk_manager.assess_portfolio_risk(
                portfolio=self.current_portfolio,
                prices=current_prices
            )
            
            # Check if risk limits are exceeded
            if risk_assessment['risk_level'] == 'HIGH':
                self.logger.warning("High risk detected, applying defensive rebalancing")
                # Apply more conservative parameters
                rebalance_result = self.optimizer.rebalance_portfolio(
                    current_portfolio=self.current_portfolio,
                    universe=self.universe,
                    target_stocks=self.target_stocks,
                    lookback_days=self.lookback_days,
                    max_turnover=min(self.max_turnover, 0.2),  # More conservative
                    min_turnover=self.min_turnover
                )
            else:
                # Normal rebalancing
                rebalance_result = self.optimizer.rebalance_portfolio(
                    current_portfolio=self.current_portfolio,
                    universe=self.universe,
                    target_stocks=self.target_stocks,
                    lookback_days=self.lookback_days,
                    max_turnover=self.max_turnover,
                    min_turnover=self.min_turnover
                )
            
            # Execute trades
            trades_executed = self._execute_trades(rebalance_result)
            
            # Update portfolio state
            self.current_portfolio = rebalance_result['new_portfolio']
            self.portfolio_weights = {
                stock: 1.0/len(self.current_portfolio) 
                for stock in self.current_portfolio
            }
            self.last_rebalance = datetime.now()
            
            # Record trading history
            trade_record = {
                'timestamp': self.last_rebalance,
                'old_portfolio': rebalance_result['current_portfolio'],
                'new_portfolio': self.current_portfolio,
                'stocks_bought': rebalance_result['stocks_to_buy'],
                'stocks_sold': rebalance_result['stocks_to_sell'],
                'turnover': rebalance_result['turnover'],
                'risk_assessment': risk_assessment,
                'trades_executed': trades_executed,
                'optimization_result': rebalance_result['optimal_result']
            }
            
            self.trading_history.append(trade_record)
            
            # Trigger callbacks
            if self.on_rebalance_callback:
                self.on_rebalance_callback(trade_record)
            
            self.logger.info(f"Rebalance completed. New portfolio: {self.current_portfolio}")
            self.logger.info(f"Turnover: {rebalance_result['turnover']:.2%}")
            
        except Exception as e:
            self.logger.error(f"Error during rebalancing: {e}")
            if self.on_error_callback:
                self.on_error_callback(e)
    
    def _execute_trades(self, rebalance_result: Dict) -> List[Dict]:
        """
        Execute trades (simulation for now).
        
        Args:
            rebalance_result: Result from portfolio rebalancing
            
        Returns:
            List of executed trades
        """
        trades = []
        
        # Get current prices for trade execution
        try:
            current_prices = self.data_provider.get_prices(
                symbols=list(set(rebalance_result['stocks_to_buy'] + 
                               rebalance_result['stocks_to_sell'])),
                start_date=(datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d'),
                end_date=datetime.now().strftime('%Y-%m-%d')
            )
            
            if not current_prices.empty:
                latest_prices = current_prices.iloc[-1]
                
                # Sell orders
                for stock in rebalance_result['stocks_to_sell']:
                    trade = {
                        'timestamp': datetime.now(),
                        'symbol': stock,
                        'action': 'SELL',
                        'price': latest_prices.get(stock, 0),
                        'status': 'EXECUTED'  # Simulated execution
                    }
                    trades.append(trade)
                    
                    if self.on_trade_callback:
                        self.on_trade_callback(trade)
                
                # Buy orders
                for stock in rebalance_result['stocks_to_buy']:
                    trade = {
                        'timestamp': datetime.now(),
                        'symbol': stock,
                        'action': 'BUY',
                        'price': latest_prices.get(stock, 0),
                        'status': 'EXECUTED'  # Simulated execution
                    }
                    trades.append(trade)
                    
                    if self.on_trade_callback:
                        self.on_trade_callback(trade)
                        
        except Exception as e:
            self.logger.error(f"Error executing trades: {e}")
        
        return trades
    
    def _update_performance_metrics(self):
        """Update portfolio performance metrics."""
        if not self.current_portfolio or not self.trading_history:
            return
            
        try:
            # Get recent price data
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            prices = self.data_provider.get_prices(
                self.current_portfolio, start_date, end_date
            )
            
            if not prices.empty:
                returns = prices.pct_change().dropna()
                
                # Calculate portfolio return
                portfolio_returns = returns.mean(axis=1)  # Equal weight assumption
                
                # Performance metrics
                self.performance_metrics = {
                    'total_return': portfolio_returns.sum(),
                    'annualized_return': portfolio_returns.mean() * 252,
                    'volatility': portfolio_returns.std() * np.sqrt(252),
                    'sharpe_ratio': (portfolio_returns.mean() * 252) / (portfolio_returns.std() * np.sqrt(252)),
                    'max_drawdown': self._calculate_max_drawdown(portfolio_returns),
                    'current_portfolio_size': len(self.current_portfolio),
                    'last_update': datetime.now()
                }
                
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown."""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def get_portfolio_status(self) -> Dict[str, Any]:
        """Get current portfolio status."""
        return {
            'current_portfolio': self.current_portfolio,
            'portfolio_weights': self.portfolio_weights,
            'is_running': self.is_running,
            'last_rebalance': self.last_rebalance,
            'performance_metrics': self.performance_metrics,
            'trading_history_length': len(self.trading_history),
            'system_parameters': {
                'target_stocks': self.target_stocks,
                'rebalance_frequency': self.rebalance_frequency,
                'lookback_days': self.lookback_days,
                'max_turnover': self.max_turnover,
                'min_turnover': self.min_turnover
            }
        }
    
    def set_callbacks(self,
                     on_rebalance: Optional[Callable] = None,
                     on_trade: Optional[Callable] = None,
                     on_error: Optional[Callable] = None):
        """Set callback functions for system events."""
        self.on_rebalance_callback = on_rebalance
        self.on_trade_callback = on_trade
        self.on_error_callback = on_error
