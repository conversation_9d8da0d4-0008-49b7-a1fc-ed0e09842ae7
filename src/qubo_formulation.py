"""
QUBO Formulation for Stock Selection Problem

This module implements the QUBO (Quadratic Unconstrained Binary Optimization) 
formulation for the stock selection problem as described in the paper.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from scipy.stats import pearsonr


class QUBOFormulation:
    """
    QUBO formulation for stock selection problem.
    
    The objective function is:
    H = H_profit + λ₁H_correlation + λ₂H_balance + λ₃H_constraints
    
    Where:
    - H_profit: Profitability term (negative expected returns)
    - H_correlation: Correlation penalty term
    - H_balance: Portfolio balance term  
    - H_constraints: Hard constraints (number of stocks, etc.)
    """
    
    def __init__(self, 
                 lambda_correlation: float = 1.0,
                 lambda_balance: float = 1.0, 
                 lambda_constraints: float = 10.0):
        """
        Initialize QUBO formulation.
        
        Args:
            lambda_correlation: Weight for correlation penalty
            lambda_balance: Weight for balance penalty
            lambda_constraints: Weight for constraint penalties
        """
        self.lambda_correlation = lambda_correlation
        self.lambda_balance = lambda_balance
        self.lambda_constraints = lambda_constraints
        
    def formulate_qubo(self, 
                      returns: pd.DataFrame,
                      target_stocks: int,
                      sector_info: Optional[Dict[str, str]] = None,
                      max_sector_weight: float = 0.5) -> Tuple[np.ndarray, np.ndarray]:
        """
        Formulate the QUBO problem for stock selection.
        
        Args:
            returns: DataFrame with stock returns (stocks as columns, dates as rows)
            target_stocks: Target number of stocks to select
            sector_info: Dictionary mapping stock symbols to sectors
            max_sector_weight: Maximum weight per sector
            
        Returns:
            Tuple of (Q matrix, linear coefficients)
        """
        n_stocks = len(returns.columns)
        
        # Calculate expected returns and correlation matrix
        expected_returns = returns.mean().values
        correlation_matrix = returns.corr().values
        
        # Initialize Q matrix (quadratic terms) and linear coefficients
        Q = np.zeros((n_stocks, n_stocks))
        linear = np.zeros(n_stocks)
        
        # 1. Profitability term: H_profit = -Σᵢ rᵢxᵢ
        # This becomes a linear term in the QUBO formulation
        linear -= expected_returns
        
        # 2. Correlation penalty: H_correlation = Σᵢⱼ ρᵢⱼxᵢxⱼ
        for i in range(n_stocks):
            for j in range(i+1, n_stocks):
                Q[i, j] += self.lambda_correlation * correlation_matrix[i, j]
                
        # 3. Balance term: Encourage equal weights
        # H_balance = Σᵢⱼ xᵢxⱼ - (1/n)Σᵢ xᵢ
        for i in range(n_stocks):
            for j in range(i+1, n_stocks):
                Q[i, j] += self.lambda_balance
            linear[i] -= self.lambda_balance / n_stocks
            
        # 4. Constraint: Exactly target_stocks stocks
        # (Σᵢ xᵢ - target_stocks)² = Σᵢ xᵢ + 2Σᵢⱼ xᵢxⱼ - 2*target_stocks*Σᵢ xᵢ + target_stocks²
        for i in range(n_stocks):
            linear[i] += self.lambda_constraints * (1 - 2 * target_stocks)
            for j in range(i+1, n_stocks):
                Q[i, j] += 2 * self.lambda_constraints
                
        # 5. Sector constraints (if provided)
        if sector_info is not None:
            self._add_sector_constraints(Q, linear, sector_info, max_sector_weight, n_stocks)
            
        return Q, linear
    
    def _add_sector_constraints(self, 
                               Q: np.ndarray, 
                               linear: np.ndarray,
                               sector_info: Dict[str, str],
                               max_sector_weight: float,
                               n_stocks: int):
        """Add sector diversification constraints."""
        # Group stocks by sector
        sectors = {}
        for i, (stock, sector) in enumerate(sector_info.items()):
            if sector not in sectors:
                sectors[sector] = []
            sectors[sector].append(i)
            
        # Add penalty for exceeding sector limits
        max_stocks_per_sector = int(max_sector_weight * n_stocks)
        
        for sector_stocks in sectors.values():
            if len(sector_stocks) > max_stocks_per_sector:
                # Penalty: (Σᵢ∈sector xᵢ - max_stocks_per_sector)²
                for i in sector_stocks:
                    linear[i] += self.lambda_constraints * (1 - 2 * max_stocks_per_sector)
                    for j in sector_stocks:
                        if i < j:
                            Q[i, j] += 2 * self.lambda_constraints
    
    def create_pyqubo_model(self, 
                           returns: pd.DataFrame,
                           target_stocks: int,
                           sector_info: Optional[Dict[str, str]] = None,
                           max_sector_weight: float = 0.5):
        """
        Create a PyQUBO model for the stock selection problem.
        
        Args:
            returns: DataFrame with stock returns
            target_stocks: Target number of stocks to select
            sector_info: Dictionary mapping stock symbols to sectors
            max_sector_weight: Maximum weight per sector
            
        Returns:
            PyQUBO model
        """
        try:
            from pyqubo import Binary, Constraint
        except ImportError:
            raise ImportError("PyQUBO is required for this functionality. Install with: pip install pyqubo")
            
        stocks = returns.columns.tolist()
        n_stocks = len(stocks)
        
        # Create binary variables
        x = {stock: Binary(stock) for stock in stocks}
        
        # Calculate expected returns and correlation matrix
        expected_returns = returns.mean()
        correlation_matrix = returns.corr()
        
        # Objective function components
        H = 0
        
        # 1. Profitability term (maximize expected returns)
        H -= sum(expected_returns[stock] * x[stock] for stock in stocks)
        
        # 2. Correlation penalty
        H += self.lambda_correlation * sum(
            correlation_matrix.loc[stock1, stock2] * x[stock1] * x[stock2]
            for i, stock1 in enumerate(stocks)
            for j, stock2 in enumerate(stocks)
            if i < j
        )
        
        # 3. Balance term
        n_selected = sum(x[stock] for stock in stocks)
        H += self.lambda_balance * sum(
            x[stock1] * x[stock2]
            for i, stock1 in enumerate(stocks)
            for j, stock2 in enumerate(stocks)
            if i < j
        )
        
        # 4. Constraint: exactly target_stocks stocks
        constraint_stocks = Constraint(
            (sum(x[stock] for stock in stocks) - target_stocks) ** 2,
            label="num_stocks"
        )
        H += self.lambda_constraints * constraint_stocks
        
        # 5. Sector constraints
        if sector_info is not None:
            sectors = {}
            for stock, sector in sector_info.items():
                if stock in stocks:
                    if sector not in sectors:
                        sectors[sector] = []
                    sectors[sector].append(stock)
            
            max_stocks_per_sector = int(max_sector_weight * n_stocks)
            for sector, sector_stocks in sectors.items():
                if len(sector_stocks) > 1:
                    sector_constraint = Constraint(
                        (sum(x[stock] for stock in sector_stocks) - max_stocks_per_sector) ** 2,
                        label=f"sector_{sector}"
                    )
                    H += self.lambda_constraints * sector_constraint
        
        return H
    
    def validate_solution(self, 
                         solution: Dict[str, int],
                         target_stocks: int,
                         sector_info: Optional[Dict[str, str]] = None,
                         max_sector_weight: float = 0.5) -> Dict[str, bool]:
        """
        Validate if a solution satisfies all constraints.
        
        Args:
            solution: Dictionary mapping stock symbols to binary values
            target_stocks: Target number of stocks
            sector_info: Sector information
            max_sector_weight: Maximum sector weight
            
        Returns:
            Dictionary with validation results
        """
        validation = {}
        
        # Check number of stocks constraint
        selected_stocks = sum(solution.values())
        validation['num_stocks_ok'] = (selected_stocks == target_stocks)
        validation['selected_count'] = selected_stocks
        
        # Check sector constraints
        if sector_info is not None:
            sectors = {}
            for stock, value in solution.items():
                if value == 1 and stock in sector_info:
                    sector = sector_info[stock]
                    sectors[sector] = sectors.get(sector, 0) + 1
            
            max_stocks_per_sector = int(max_sector_weight * len(solution))
            validation['sector_constraints_ok'] = all(
                count <= max_stocks_per_sector 
                for count in sectors.values()
            )
            validation['sector_distribution'] = sectors
        else:
            validation['sector_constraints_ok'] = True
            validation['sector_distribution'] = {}
            
        return validation

    def calculate_objective_value(self,
                                 solution: Dict[str, int],
                                 returns: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate the objective function value for a given solution.

        Args:
            solution: Dictionary mapping stock symbols to binary values
            returns: DataFrame with stock returns

        Returns:
            Dictionary with objective function components
        """
        selected_stocks = [stock for stock, value in solution.items() if value == 1]

        if not selected_stocks:
            return {
                'total_objective': 0.0,
                'profitability': 0.0,
                'correlation_penalty': 0.0,
                'balance_penalty': 0.0,
                'expected_return': 0.0,
                'avg_correlation': 0.0
            }

        # Calculate expected returns
        expected_returns = returns[selected_stocks].mean()
        total_expected_return = expected_returns.sum()

        # Calculate correlation penalty
        if len(selected_stocks) > 1:
            corr_matrix = returns[selected_stocks].corr()
            # Average pairwise correlation
            avg_correlation = corr_matrix.values[np.triu_indices_from(corr_matrix.values, k=1)].mean()
            correlation_penalty = self.lambda_correlation * avg_correlation
        else:
            avg_correlation = 0.0
            correlation_penalty = 0.0

        # Calculate balance penalty (deviation from equal weights)
        n_selected = len(selected_stocks)
        equal_weight = 1.0 / n_selected if n_selected > 0 else 0.0
        balance_penalty = self.lambda_balance * (1.0 - equal_weight) ** 2

        # Total objective (note: we minimize, so negative returns are good)
        profitability = -total_expected_return
        total_objective = profitability + correlation_penalty + balance_penalty

        return {
            'total_objective': total_objective,
            'profitability': profitability,
            'correlation_penalty': correlation_penalty,
            'balance_penalty': balance_penalty,
            'expected_return': total_expected_return,
            'avg_correlation': avg_correlation
        }
