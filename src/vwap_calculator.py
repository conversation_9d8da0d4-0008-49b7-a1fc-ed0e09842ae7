"""
VWAP (Volume Weighted Average Price) Calculator

This module implements various VWAP calculations commonly used in trading systems
for execution benchmarking, price analysis, and trading signals.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import warnings
warnings.filterwarnings('ignore')


class VWAPCalculator:
    """
    Volume Weighted Average Price calculator with multiple VWAP variants.
    """
    
    def __init__(self):
        """Initialize VWAP calculator."""
        pass
    
    def calculate_vwap(self, 
                      data: pd.DataFrame,
                      price_column: str = 'Close',
                      volume_column: str = 'Volume',
                      use_typical_price: bool = True) -> pd.Series:
        """
        Calculate standard VWAP.
        
        VWAP = Σ(Price × Volume) / Σ(Volume)
        
        Args:
            data: DataFrame with OHLCV data
            price_column: Column to use for price (if not using typical price)
            volume_column: Column name for volume
            use_typical_price: If True, use (H+L+C)/3, otherwise use specified price_column
            
        Returns:
            Series with VWAP values
        """
        if data.empty:
            return pd.Series(dtype=float)
        
        # Calculate typical price if requested
        if use_typical_price and all(col in data.columns for col in ['High', 'Low', 'Close']):
            typical_price = (data['High'] + data['Low'] + data['Close']) / 3
        else:
            typical_price = data[price_column]
        
        # Calculate price * volume
        pv = typical_price * data[volume_column]
        
        # Calculate cumulative VWAP
        cumulative_pv = pv.cumsum()
        cumulative_volume = data[volume_column].cumsum()
        
        # Avoid division by zero
        vwap = cumulative_pv / cumulative_volume.replace(0, np.nan)
        
        return vwap
    
    def calculate_rolling_vwap(self, 
                              data: pd.DataFrame,
                              window: int,
                              price_column: str = 'Close',
                              volume_column: str = 'Volume',
                              use_typical_price: bool = True) -> pd.Series:
        """
        Calculate rolling VWAP over a specified window.
        
        Args:
            data: DataFrame with OHLCV data
            window: Rolling window size
            price_column: Column to use for price
            volume_column: Column name for volume
            use_typical_price: If True, use typical price (H+L+C)/3
            
        Returns:
            Series with rolling VWAP values
        """
        if data.empty:
            return pd.Series(dtype=float)
        
        # Calculate typical price if requested
        if use_typical_price and all(col in data.columns for col in ['High', 'Low', 'Close']):
            typical_price = (data['High'] + data['Low'] + data['Close']) / 3
        else:
            typical_price = data[price_column]
        
        # Calculate price * volume
        pv = typical_price * data[volume_column]
        
        # Calculate rolling sums
        rolling_pv = pv.rolling(window=window).sum()
        rolling_volume = data[volume_column].rolling(window=window).sum()
        
        # Calculate rolling VWAP
        rolling_vwap = rolling_pv / rolling_volume.replace(0, np.nan)
        
        return rolling_vwap
    
    def calculate_session_vwap(self, 
                              data: pd.DataFrame,
                              session_start: str = '09:30',
                              session_end: str = '16:00',
                              price_column: str = 'Close',
                              volume_column: str = 'Volume',
                              use_typical_price: bool = True) -> pd.Series:
        """
        Calculate VWAP for trading sessions (resets each day).
        
        Args:
            data: DataFrame with intraday OHLCV data
            session_start: Session start time (HH:MM format)
            session_end: Session end time (HH:MM format)
            price_column: Column to use for price
            volume_column: Column name for volume
            use_typical_price: If True, use typical price
            
        Returns:
            Series with session VWAP values
        """
        if data.empty:
            return pd.Series(dtype=float)
        
        # Ensure data has datetime index
        if not isinstance(data.index, pd.DatetimeIndex):
            return pd.Series(dtype=float, index=data.index)
        
        # Calculate typical price if requested
        if use_typical_price and all(col in data.columns for col in ['High', 'Low', 'Close']):
            typical_price = (data['High'] + data['Low'] + data['Close']) / 3
        else:
            typical_price = data[price_column]
        
        # Filter for trading session hours
        session_mask = (data.index.time >= pd.to_datetime(session_start).time()) & \
                      (data.index.time <= pd.to_datetime(session_end).time())
        
        session_data = data[session_mask].copy()
        session_typical_price = typical_price[session_mask]
        
        if session_data.empty:
            return pd.Series(dtype=float, index=data.index)
        
        # Calculate price * volume
        pv = session_typical_price * session_data[volume_column]
        
        # Group by date and calculate daily VWAP
        session_vwap = pd.Series(index=data.index, dtype=float)
        
        for date in session_data.index.date:
            date_mask = session_data.index.date == date
            date_data = session_data[date_mask]
            date_pv = pv[date_mask]
            
            if not date_data.empty:
                cumulative_pv = date_pv.cumsum()
                cumulative_volume = date_data[volume_column].cumsum()
                daily_vwap = cumulative_pv / cumulative_volume.replace(0, np.nan)
                
                session_vwap.loc[date_data.index] = daily_vwap
        
        return session_vwap
    
    def calculate_anchored_vwap(self, 
                               data: pd.DataFrame,
                               anchor_date: Union[str, pd.Timestamp],
                               price_column: str = 'Close',
                               volume_column: str = 'Volume',
                               use_typical_price: bool = True) -> pd.Series:
        """
        Calculate anchored VWAP from a specific date/time.
        
        Args:
            data: DataFrame with OHLCV data
            anchor_date: Starting date/time for VWAP calculation
            price_column: Column to use for price
            volume_column: Column name for volume
            use_typical_price: If True, use typical price
            
        Returns:
            Series with anchored VWAP values
        """
        if data.empty:
            return pd.Series(dtype=float)
        
        # Convert anchor_date to timestamp
        if isinstance(anchor_date, str):
            anchor_date = pd.to_datetime(anchor_date)
        
        # Filter data from anchor date onwards
        anchored_data = data[data.index >= anchor_date].copy()
        
        if anchored_data.empty:
            return pd.Series(dtype=float, index=data.index)
        
        # Calculate typical price if requested
        if use_typical_price and all(col in anchored_data.columns for col in ['High', 'Low', 'Close']):
            typical_price = (anchored_data['High'] + anchored_data['Low'] + anchored_data['Close']) / 3
        else:
            typical_price = anchored_data[price_column]
        
        # Calculate price * volume
        pv = typical_price * anchored_data[volume_column]
        
        # Calculate cumulative VWAP from anchor point
        cumulative_pv = pv.cumsum()
        cumulative_volume = anchored_data[volume_column].cumsum()
        
        anchored_vwap = cumulative_pv / cumulative_volume.replace(0, np.nan)
        
        # Create full series with NaN before anchor date
        full_vwap = pd.Series(index=data.index, dtype=float)
        full_vwap.loc[anchored_data.index] = anchored_vwap
        
        return full_vwap
    
    def calculate_vwap_bands(self, 
                            vwap: pd.Series,
                            data: pd.DataFrame,
                            num_std: float = 1.0,
                            volume_column: str = 'Volume') -> Dict[str, pd.Series]:
        """
        Calculate VWAP bands (standard deviation bands around VWAP).
        
        Args:
            vwap: VWAP series
            data: Original OHLCV data
            num_std: Number of standard deviations for bands
            volume_column: Volume column name
            
        Returns:
            Dictionary with 'upper_band' and 'lower_band' series
        """
        if vwap.empty or data.empty:
            return {'upper_band': pd.Series(dtype=float), 'lower_band': pd.Series(dtype=float)}
        
        # Calculate typical price
        if all(col in data.columns for col in ['High', 'Low', 'Close']):
            typical_price = (data['High'] + data['Low'] + data['Close']) / 3
        else:
            typical_price = data['Close']
        
        # Calculate volume-weighted variance
        price_diff_sq = (typical_price - vwap) ** 2
        volume_weighted_var = (price_diff_sq * data[volume_column]).cumsum() / data[volume_column].cumsum()
        
        # Calculate standard deviation
        vwap_std = np.sqrt(volume_weighted_var)
        
        # Calculate bands
        upper_band = vwap + (num_std * vwap_std)
        lower_band = vwap - (num_std * vwap_std)
        
        return {
            'upper_band': upper_band,
            'lower_band': lower_band,
            'vwap_std': vwap_std
        }
    
    def calculate_vwap_signals(self, 
                              data: pd.DataFrame,
                              vwap: pd.Series,
                              price_column: str = 'Close') -> Dict[str, pd.Series]:
        """
        Generate trading signals based on VWAP.
        
        Args:
            data: OHLCV data
            vwap: VWAP series
            price_column: Price column for comparison
            
        Returns:
            Dictionary with various VWAP-based signals
        """
        if data.empty or vwap.empty:
            return {}
        
        price = data[price_column]
        
        # Basic signals
        above_vwap = price > vwap
        below_vwap = price < vwap
        
        # Crossover signals
        price_cross_above = (price > vwap) & (price.shift(1) <= vwap.shift(1))
        price_cross_below = (price < vwap) & (price.shift(1) >= vwap.shift(1))
        
        # Distance from VWAP (as percentage)
        vwap_distance_pct = ((price - vwap) / vwap * 100).fillna(0)
        
        # VWAP slope (trend direction)
        vwap_slope = vwap.diff()
        vwap_trend_up = vwap_slope > 0
        vwap_trend_down = vwap_slope < 0
        
        return {
            'above_vwap': above_vwap,
            'below_vwap': below_vwap,
            'cross_above': price_cross_above,
            'cross_below': price_cross_below,
            'distance_pct': vwap_distance_pct,
            'vwap_slope': vwap_slope,
            'trend_up': vwap_trend_up,
            'trend_down': vwap_trend_down
        }
    
    def calculate_multiple_vwaps(self, 
                                data: pd.DataFrame,
                                windows: List[int] = [20, 50, 100],
                                use_typical_price: bool = True) -> Dict[str, pd.Series]:
        """
        Calculate multiple rolling VWAPs with different windows.
        
        Args:
            data: OHLCV data
            windows: List of window sizes
            use_typical_price: Whether to use typical price
            
        Returns:
            Dictionary mapping window sizes to VWAP series
        """
        vwaps = {}
        
        for window in windows:
            vwap_key = f'vwap_{window}'
            vwaps[vwap_key] = self.calculate_rolling_vwap(
                data=data,
                window=window,
                use_typical_price=use_typical_price
            )
        
        return vwaps
    
    def vwap_execution_analysis(self, 
                               execution_prices: List[float],
                               execution_volumes: List[float],
                               benchmark_vwap: float) -> Dict[str, float]:
        """
        Analyze execution performance against VWAP benchmark.
        
        Args:
            execution_prices: List of execution prices
            execution_volumes: List of execution volumes
            benchmark_vwap: Benchmark VWAP price
            
        Returns:
            Dictionary with execution analysis metrics
        """
        if not execution_prices or not execution_volumes:
            return {}
        
        execution_prices = np.array(execution_prices)
        execution_volumes = np.array(execution_volumes)
        
        # Calculate execution VWAP
        total_volume = execution_volumes.sum()
        if total_volume == 0:
            return {}
        
        execution_vwap = (execution_prices * execution_volumes).sum() / total_volume
        
        # Performance metrics
        vwap_slippage = execution_vwap - benchmark_vwap
        vwap_slippage_pct = (vwap_slippage / benchmark_vwap) * 100 if benchmark_vwap != 0 else 0
        
        # Execution statistics
        avg_price = execution_prices.mean()
        price_std = execution_prices.std()
        volume_weighted_std = np.sqrt(((execution_prices - execution_vwap)**2 * execution_volumes).sum() / total_volume)
        
        return {
            'execution_vwap': execution_vwap,
            'benchmark_vwap': benchmark_vwap,
            'vwap_slippage': vwap_slippage,
            'vwap_slippage_pct': vwap_slippage_pct,
            'avg_execution_price': avg_price,
            'price_std': price_std,
            'volume_weighted_std': volume_weighted_std,
            'total_volume': total_volume,
            'num_executions': len(execution_prices)
        }
