#!/usr/bin/env python3
"""
Simple test of the QUBO formulation without external dependencies.
This demonstrates the core mathematical concepts from the paper.
"""

import random
import math


class SimpleQUBOFormulation:
    """
    Simplified QUBO formulation for stock selection without external dependencies.
    
    This demonstrates the core concepts from the paper:
    "Real-Time Trading System Based on Selections of Potentially Profitable,
    Uncorrelated, and Balanced Stocks by NP-Hard Combinatorial Optimization"
    """
    
    def __init__(self, lambda_correlation=1.0, lambda_balance=1.0, lambda_constraints=10.0):
        self.lambda_correlation = lambda_correlation
        self.lambda_balance = lambda_balance
        self.lambda_constraints = lambda_constraints
    
    def create_sample_data(self, n_stocks=10, n_days=100, seed=42):
        """Create sample return data and correlation matrix."""
        random.seed(seed)
        
        # Generate stock symbols
        stocks = [f"STOCK_{i:02d}" for i in range(n_stocks)]
        
        # Generate expected returns (daily)
        expected_returns = [random.uniform(-0.001, 0.002) for _ in range(n_stocks)]
        
        # Generate correlation matrix
        correlation_matrix = []
        for i in range(n_stocks):
            row = []
            for j in range(n_stocks):
                if i == j:
                    row.append(1.0)
                elif i < j:
                    corr = random.uniform(0.1, 0.7)
                    row.append(corr)
                else:
                    row.append(correlation_matrix[j][i])  # Symmetric
            correlation_matrix.append(row)
        
        return stocks, expected_returns, correlation_matrix
    
    def formulate_qubo_simple(self, expected_returns, correlation_matrix, target_stocks):
        """
        Formulate QUBO problem for stock selection.
        
        The objective function is:
        H = H_profit + λ₁H_correlation + λ₂H_balance + λ₃H_constraints
        
        Returns Q matrix and linear coefficients for QUBO formulation.
        """
        n_stocks = len(expected_returns)
        
        # Initialize Q matrix (quadratic terms) and linear coefficients
        Q = [[0.0 for _ in range(n_stocks)] for _ in range(n_stocks)]
        linear = [0.0 for _ in range(n_stocks)]
        
        # 1. Profitability term: H_profit = -Σᵢ rᵢxᵢ (maximize expected returns)
        for i in range(n_stocks):
            linear[i] -= expected_returns[i]
        
        # 2. Correlation penalty: H_correlation = λ₁ * Σᵢⱼ ρᵢⱼxᵢxⱼ
        for i in range(n_stocks):
            for j in range(i+1, n_stocks):
                Q[i][j] += self.lambda_correlation * correlation_matrix[i][j]
        
        # 3. Balance term: Encourage equal weights
        for i in range(n_stocks):
            for j in range(i+1, n_stocks):
                Q[i][j] += self.lambda_balance
            linear[i] -= self.lambda_balance / n_stocks
        
        # 4. Constraint: Exactly target_stocks stocks
        # (Σᵢ xᵢ - target_stocks)² penalty
        for i in range(n_stocks):
            linear[i] += self.lambda_constraints * (1 - 2 * target_stocks)
            for j in range(i+1, n_stocks):
                Q[i][j] += 2 * self.lambda_constraints
        
        return Q, linear
    
    def evaluate_solution(self, solution, expected_returns, correlation_matrix):
        """Evaluate a binary solution vector."""
        n_stocks = len(solution)
        selected_indices = [i for i in range(n_stocks) if solution[i] == 1]
        
        if not selected_indices:
            return {
                'expected_return': 0.0,
                'avg_correlation': 0.0,
                'num_selected': 0,
                'objective_value': float('inf')
            }
        
        # Calculate expected return
        total_return = sum(expected_returns[i] for i in selected_indices)
        avg_return = total_return / len(selected_indices)
        
        # Calculate average correlation
        if len(selected_indices) > 1:
            correlations = []
            for i in range(len(selected_indices)):
                for j in range(i+1, len(selected_indices)):
                    idx1, idx2 = selected_indices[i], selected_indices[j]
                    correlations.append(correlation_matrix[idx1][idx2])
            avg_correlation = sum(correlations) / len(correlations)
        else:
            avg_correlation = 0.0
        
        # Calculate objective value components
        profit_term = -total_return  # Negative because we minimize
        correlation_penalty = self.lambda_correlation * avg_correlation
        balance_penalty = self.lambda_balance * (1.0 - 1.0/len(selected_indices))**2
        
        # Constraint penalty
        target_stocks = 5  # Assume target
        constraint_penalty = self.lambda_constraints * (len(selected_indices) - target_stocks)**2
        
        objective_value = profit_term + correlation_penalty + balance_penalty + constraint_penalty
        
        return {
            'expected_return': avg_return,
            'avg_correlation': avg_correlation,
            'num_selected': len(selected_indices),
            'objective_value': objective_value,
            'profit_term': profit_term,
            'correlation_penalty': correlation_penalty,
            'balance_penalty': balance_penalty,
            'constraint_penalty': constraint_penalty
        }
    
    def simple_greedy_solver(self, Q, linear, target_stocks):
        """
        Simple greedy solver for demonstration.
        In practice, this would be replaced by OpenJij.
        """
        n_stocks = len(linear)
        
        # Start with empty solution
        solution = [0] * n_stocks
        
        # Greedy selection
        for _ in range(target_stocks):
            best_improvement = float('inf')
            best_index = -1
            
            for i in range(n_stocks):
                if solution[i] == 0:  # Not yet selected
                    # Try adding this stock
                    test_solution = solution[:]
                    test_solution[i] = 1
                    
                    # Calculate objective value
                    obj_value = self.calculate_qubo_objective(test_solution, Q, linear)
                    
                    if obj_value < best_improvement:
                        best_improvement = obj_value
                        best_index = i
            
            if best_index >= 0:
                solution[best_index] = 1
        
        return solution
    
    def calculate_qubo_objective(self, solution, Q, linear):
        """Calculate QUBO objective value."""
        n = len(solution)
        obj_value = 0.0
        
        # Linear terms
        for i in range(n):
            obj_value += linear[i] * solution[i]
        
        # Quadratic terms
        for i in range(n):
            for j in range(i+1, n):
                obj_value += Q[i][j] * solution[i] * solution[j]
        
        return obj_value


def main():
    """Run the simple demonstration."""
    print("="*60)
    print("SIMPLE QUBO STOCK SELECTION DEMONSTRATION")
    print("Based on: Real-Time Trading System with NP-Hard Optimization")
    print("="*60)
    
    # Initialize QUBO formulation
    qubo = SimpleQUBOFormulation(
        lambda_correlation=2.0,
        lambda_balance=1.0,
        lambda_constraints=10.0
    )
    
    # Create sample data
    print("\n1. Creating sample data...")
    stocks, expected_returns, correlation_matrix = qubo.create_sample_data(n_stocks=10, seed=42)
    
    print(f"Number of stocks: {len(stocks)}")
    print(f"Stock symbols: {stocks}")
    print(f"Expected returns: {[f'{r:.4f}' for r in expected_returns]}")
    
    # Show correlation matrix (first few entries)
    print(f"\nSample correlations:")
    for i in range(min(3, len(stocks))):
        for j in range(i+1, min(3, len(stocks))):
            print(f"  {stocks[i]} - {stocks[j]}: {correlation_matrix[i][j]:.3f}")
    
    # Formulate QUBO problem
    print(f"\n2. Formulating QUBO problem...")
    target_stocks = 5
    Q, linear = qubo.formulate_qubo_simple(expected_returns, correlation_matrix, target_stocks)
    
    print(f"Target portfolio size: {target_stocks}")
    print(f"QUBO matrix size: {len(Q)}x{len(Q[0])}")
    print(f"Linear coefficients: {[f'{c:.4f}' for c in linear]}")
    
    # Solve using simple greedy algorithm
    print(f"\n3. Solving with greedy algorithm...")
    solution = qubo.simple_greedy_solver(Q, linear, target_stocks)
    
    selected_stocks = [stocks[i] for i in range(len(solution)) if solution[i] == 1]
    print(f"Selected stocks: {selected_stocks}")
    print(f"Solution vector: {solution}")
    
    # Evaluate solution
    print(f"\n4. Evaluating solution...")
    evaluation = qubo.evaluate_solution(solution, expected_returns, correlation_matrix)
    
    print(f"Number of stocks selected: {evaluation['num_selected']}")
    print(f"Average expected return: {evaluation['expected_return']:.4f}")
    print(f"Average correlation: {evaluation['avg_correlation']:.4f}")
    print(f"Total objective value: {evaluation['objective_value']:.4f}")
    
    print(f"\nObjective breakdown:")
    print(f"  Profit term: {evaluation['profit_term']:.4f}")
    print(f"  Correlation penalty: {evaluation['correlation_penalty']:.4f}")
    print(f"  Balance penalty: {evaluation['balance_penalty']:.4f}")
    print(f"  Constraint penalty: {evaluation['constraint_penalty']:.4f}")
    
    # Compare with random selection
    print(f"\n5. Comparison with random selection...")
    random.seed(123)
    random_indices = random.sample(range(len(stocks)), target_stocks)
    random_solution = [1 if i in random_indices else 0 for i in range(len(stocks))]
    random_stocks = [stocks[i] for i in random_indices]
    
    random_evaluation = qubo.evaluate_solution(random_solution, expected_returns, correlation_matrix)
    
    print(f"Random portfolio: {random_stocks}")
    print(f"Random avg return: {random_evaluation['expected_return']:.4f}")
    print(f"Random avg correlation: {random_evaluation['avg_correlation']:.4f}")
    print(f"Random objective value: {random_evaluation['objective_value']:.4f}")
    
    # Performance comparison
    print(f"\n6. Performance comparison:")
    print(f"{'Metric':<20} {'Optimized':<12} {'Random':<12} {'Improvement':<12}")
    print("-" * 60)
    
    return_improvement = (evaluation['expected_return'] - random_evaluation['expected_return']) / abs(random_evaluation['expected_return']) * 100
    corr_improvement = (random_evaluation['avg_correlation'] - evaluation['avg_correlation']) / random_evaluation['avg_correlation'] * 100
    obj_improvement = (random_evaluation['objective_value'] - evaluation['objective_value']) / random_evaluation['objective_value'] * 100
    
    print(f"{'Expected Return':<20} {evaluation['expected_return']:<12.4f} {random_evaluation['expected_return']:<12.4f} {return_improvement:<12.1f}%")
    print(f"{'Avg Correlation':<20} {evaluation['avg_correlation']:<12.4f} {random_evaluation['avg_correlation']:<12.4f} {corr_improvement:<12.1f}%")
    print(f"{'Objective Value':<20} {evaluation['objective_value']:<12.4f} {random_evaluation['objective_value']:<12.4f} {obj_improvement:<12.1f}%")
    
    print(f"\n" + "="*60)
    print("DEMONSTRATION COMPLETED")
    print("="*60)
    
    print(f"\nKey Insights:")
    print(f"1. The QUBO formulation successfully balances multiple objectives")
    print(f"2. Optimized portfolio shows {'better' if obj_improvement > 0 else 'worse'} objective value than random")
    print(f"3. Correlation is {'reduced' if corr_improvement > 0 else 'increased'} compared to random selection")
    print(f"4. Expected return is {'improved' if return_improvement > 0 else 'reduced'} vs random")
    
    print(f"\nNext Steps:")
    print(f"1. Install OpenJij: pip install openjij")
    print(f"2. Run full implementation: python run_demo.py")
    print(f"3. Experiment with different lambda parameters")
    print(f"4. Test with real market data using Yahoo Finance")


if __name__ == "__main__":
    main()
