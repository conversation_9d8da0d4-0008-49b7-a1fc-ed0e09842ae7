#!/usr/bin/env python3
"""
Comprehensive Demo Script for Stock Optimization System

This script demonstrates all the key features of the stock optimization system
based on the paper "Real-Time Trading System Based on Selections of Potentially
Profitable, Uncorrelated, and Balanced Stocks by NP-Hard Combinatorial Optimization"
using OpenJij instead of SBM solver.
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_provider import SyntheticDataProvider, create_sample_data
from stock_optimizer import StockOptimizer
from qubo_formulation import QUBOFormulation
from risk_manager import RiskManager
from utils import calculate_portfolio_performance, create_optimization_report, plot_portfolio_analysis


def demo_qubo_formulation():
    """Demonstrate QUBO formulation capabilities."""
    print("\n" + "="*60)
    print("DEMO 1: QUBO FORMULATION")
    print("="*60)
    
    # Create test data
    returns, sector_info = create_sample_data(n_stocks=8, n_days=100)
    
    print(f"Created returns data: {returns.shape}")
    print(f"Stocks: {list(returns.columns)}")
    print(f"Sectors: {set(sector_info.values())}")
    
    # Initialize QUBO formulation
    qubo = QUBOFormulation(
        lambda_correlation=2.0,
        lambda_balance=1.0,
        lambda_constraints=10.0
    )
    
    # Formulate QUBO problem
    Q, linear = qubo.formulate_qubo(
        returns=returns,
        target_stocks=3,
        sector_info=sector_info,
        max_sector_weight=0.5
    )
    
    print(f"\nQUBO Matrix shape: {Q.shape}")
    print(f"Linear coefficients shape: {linear.shape}")
    print(f"Non-zero elements in Q: {np.count_nonzero(Q)}")
    print(f"Non-zero elements in linear: {np.count_nonzero(linear)}")
    
    # Test solution validation
    test_solution = {stock: 1 if i < 3 else 0 for i, stock in enumerate(returns.columns)}
    validation = qubo.validate_solution(test_solution, target_stocks=3, sector_info=sector_info)
    
    print(f"\nTest solution: {test_solution}")
    print(f"Validation result: {validation}")
    
    # Calculate objective value
    obj_value = qubo.calculate_objective_value(test_solution, returns)
    print(f"Objective components: {obj_value}")


def demo_stock_optimizer():
    """Demonstrate stock optimizer with different configurations."""
    print("\n" + "="*60)
    print("DEMO 2: STOCK OPTIMIZER")
    print("="*60)
    
    # Create data provider and optimizer
    data_provider = SyntheticDataProvider(n_stocks=15, seed=123)
    returns, sector_info = create_sample_data(n_stocks=15, n_days=200)
    universe = list(returns.columns)
    
    print(f"Universe size: {len(universe)}")
    print(f"Available sectors: {set(sector_info.values())}")
    
    # Test different optimizer configurations
    configs = [
        {"name": "Balanced", "lambda_correlation": 1.0, "lambda_balance": 1.0},
        {"name": "Low Correlation Focus", "lambda_correlation": 3.0, "lambda_balance": 0.5},
        {"name": "Balance Focus", "lambda_correlation": 0.5, "lambda_balance": 2.0},
    ]
    
    results = {}
    
    for config in configs:
        print(f"\n--- Testing {config['name']} Configuration ---")
        
        optimizer = StockOptimizer(
            data_provider=data_provider,
            lambda_correlation=config['lambda_correlation'],
            lambda_balance=config['lambda_balance'],
            lambda_constraints=10.0,
            solver_type='simulated_annealing'
        )
        
        result = optimizer.select_stocks(
            universe=universe,
            target_stocks=5,
            lookback_days=150,
            sector_info=sector_info,
            max_sector_weight=0.6,
            num_reads=500
        )
        
        results[config['name']] = result
        
        print(f"Selected stocks: {result['selected_stocks']}")
        print(f"Expected return: {result['objective_components']['expected_return']:.4f}")
        print(f"Average correlation: {result['objective_components']['avg_correlation']:.4f}")
        print(f"Optimization energy: {result['energy']:.6f}")
        print(f"Sector distribution: {result['validation']['sector_distribution']}")
    
    # Compare configurations
    print(f"\n--- Configuration Comparison ---")
    print(f"{'Configuration':<20} {'Return':<10} {'Correlation':<12} {'Energy':<12}")
    print("-" * 60)
    
    for name, result in results.items():
        obj_comp = result['objective_components']
        print(f"{name:<20} {obj_comp['expected_return']:<10.4f} "
              f"{obj_comp['avg_correlation']:<12.4f} {result['energy']:<12.6f}")
    
    return results


def demo_risk_management():
    """Demonstrate risk management capabilities."""
    print("\n" + "="*60)
    print("DEMO 3: RISK MANAGEMENT")
    print("="*60)
    
    # Create test portfolio
    returns, sector_info = create_sample_data(n_stocks=12, n_days=150)
    
    # Create price data from returns
    initial_price = 100
    prices = (1 + returns).cumprod() * initial_price
    
    # Test portfolio
    portfolio = list(returns.columns)[:6]
    weights = {stock: 1.0/len(portfolio) for stock in portfolio}
    
    print(f"Test portfolio: {portfolio}")
    print(f"Portfolio weights: {weights}")
    
    # Initialize risk manager
    risk_manager = RiskManager(
        max_portfolio_volatility=0.20,
        max_individual_weight=0.25,
        max_sector_weight=0.40,
        max_drawdown_threshold=0.10
    )
    
    # Assess portfolio risk
    risk_assessment = risk_manager.assess_portfolio_risk(
        portfolio=portfolio,
        prices=prices,
        weights=weights,
        sector_info=sector_info
    )
    
    print(f"\nRisk Assessment:")
    print(f"Risk Level: {risk_assessment['risk_level']}")
    print(f"Risk Flags: {risk_assessment['risk_flags']}")
    
    # Portfolio metrics
    portfolio_metrics = risk_assessment['portfolio_metrics']
    print(f"\nPortfolio Metrics:")
    for key, value in portfolio_metrics.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    # Recommendations
    recommendations = risk_assessment['recommendations']
    print(f"\nRisk Management Recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")
    
    # Test position sizing
    expected_returns = returns[portfolio].mean()
    covariance_matrix = returns[portfolio].cov()
    
    optimal_weights = risk_manager.calculate_position_sizes(
        expected_returns=expected_returns,
        covariance_matrix=covariance_matrix,
        risk_budget=0.15
    )
    
    print(f"\nOptimal Position Sizes (Risk Parity):")
    for stock, weight in optimal_weights.items():
        print(f"  {stock}: {weight:.3f}")


def demo_performance_comparison():
    """Demonstrate performance comparison between optimized and random portfolios."""
    print("\n" + "="*60)
    print("DEMO 4: PERFORMANCE COMPARISON")
    print("="*60)
    
    # Create larger dataset for meaningful comparison
    returns, sector_info = create_sample_data(n_stocks=25, n_days=300)
    universe = list(returns.columns)
    
    data_provider = SyntheticDataProvider(n_stocks=25, seed=456)
    optimizer = StockOptimizer(
        data_provider=data_provider,
        lambda_correlation=1.5,
        lambda_balance=1.0,
        lambda_constraints=10.0
    )
    
    # Optimized portfolio
    print("Generating optimized portfolio...")
    opt_result = optimizer.select_stocks(
        universe=universe,
        target_stocks=8,
        lookback_days=200,
        sector_info=sector_info,
        num_reads=1000
    )
    
    # Random portfolios for comparison
    print("Generating random portfolios for comparison...")
    np.random.seed(789)
    n_random = 100
    random_performances = []
    
    for i in range(n_random):
        random_stocks = np.random.choice(universe, 8, replace=False).tolist()
        random_weights = {stock: 1.0/8 for stock in random_stocks}
        random_returns = returns[random_stocks]
        
        if not random_returns.empty:
            perf = calculate_portfolio_performance(random_returns, random_weights)
            random_performances.append(perf)
    
    # Calculate optimized portfolio performance
    opt_weights = {stock: 1.0/len(opt_result['selected_stocks']) 
                  for stock in opt_result['selected_stocks']}
    opt_performance = calculate_portfolio_performance(
        opt_result['returns_data'], opt_weights
    )
    
    # Statistical comparison
    random_returns = [p['annualized_return'] for p in random_performances]
    random_sharpe = [p['sharpe_ratio'] for p in random_performances]
    random_volatility = [p['volatility'] for p in random_performances]
    
    print(f"\nPerformance Comparison (Optimized vs Random):")
    print(f"{'Metric':<20} {'Optimized':<12} {'Random Mean':<12} {'Random Std':<12} {'Percentile':<12}")
    print("-" * 80)
    
    # Calculate percentiles
    return_percentile = (np.array(random_returns) < opt_performance['annualized_return']).mean() * 100
    sharpe_percentile = (np.array(random_sharpe) < opt_performance['sharpe_ratio']).mean() * 100
    vol_percentile = (np.array(random_volatility) > opt_performance['volatility']).mean() * 100
    
    print(f"{'Return':<20} {opt_performance['annualized_return']:<12.2%} "
          f"{np.mean(random_returns):<12.2%} {np.std(random_returns):<12.2%} {return_percentile:<12.1f}")
    print(f"{'Sharpe Ratio':<20} {opt_performance['sharpe_ratio']:<12.4f} "
          f"{np.mean(random_sharpe):<12.4f} {np.std(random_sharpe):<12.4f} {sharpe_percentile:<12.1f}")
    print(f"{'Volatility':<20} {opt_performance['volatility']:<12.2%} "
          f"{np.mean(random_volatility):<12.2%} {np.std(random_volatility):<12.2%} {vol_percentile:<12.1f}")
    
    # Correlation analysis
    opt_corr_matrix = opt_result['returns_data'].corr()
    opt_avg_corr = opt_corr_matrix.values[np.triu_indices_from(opt_corr_matrix.values, k=1)].mean()
    
    random_correlations = []
    for perf_data in random_performances[:20]:  # Sample subset for correlation calculation
        random_stocks = np.random.choice(universe, 8, replace=False).tolist()
        random_corr_matrix = returns[random_stocks].corr()
        random_avg_corr = random_corr_matrix.values[np.triu_indices_from(random_corr_matrix.values, k=1)].mean()
        random_correlations.append(random_avg_corr)
    
    corr_percentile = (np.array(random_correlations) > opt_avg_corr).mean() * 100
    
    print(f"{'Avg Correlation':<20} {opt_avg_corr:<12.4f} "
          f"{np.mean(random_correlations):<12.4f} {np.std(random_correlations):<12.4f} {corr_percentile:<12.1f}")
    
    print(f"\nOptimized portfolio selected: {opt_result['selected_stocks']}")
    print(f"Sector distribution: {opt_result['validation']['sector_distribution']}")


def main():
    """Run all demonstrations."""
    print("STOCK OPTIMIZATION SYSTEM DEMONSTRATION")
    print("Based on: Real-Time Trading System with NP-Hard Combinatorial Optimization")
    print("Implementation: Using OpenJij instead of SBM solver")
    print("="*80)
    
    try:
        # Run all demos
        demo_qubo_formulation()
        demo_stock_optimizer()
        demo_risk_management()
        demo_performance_comparison()
        
        print("\n" + "="*80)
        print("ALL DEMONSTRATIONS COMPLETED SUCCESSFULLY!")
        print("="*80)
        
        print("\nNext Steps:")
        print("1. Run 'python examples/basic_optimization.py' for basic usage")
        print("2. Run 'python examples/real_time_trading.py' for real-time demo")
        print("3. Run 'python -m pytest tests/' to execute unit tests")
        print("4. Modify parameters in the examples to experiment with different configurations")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
