# Real-Time Trading System Based on Stock Selection by NP-Hard Combinatorial Optimization

This project implements the methodology described in the paper "Real-Time Trading System Based on Selections of Potentially Profitable, Uncorrelated, and Balanced Stocks by NP-Hard Combinatorial Optimization" by <PERSON><PERSON><PERSON> et al. (2023), using OpenJij instead of the original SBM solver.

## Overview

The system selects potentially profitable, uncorrelated, and balanced stocks using quantum-inspired optimization techniques. The core methodology involves:

1. **Stock Selection Problem Formulation**: Converting stock selection into a QUBO (Quadratic Unconstrained Binary Optimization) problem
2. **Multi-objective Optimization**: Balancing profitability, correlation minimization, and portfolio balance
3. **Real-time Processing**: Using OpenJij for fast optimization suitable for real-time trading

## Key Features

- **QUBO Formulation**: Mathematical formulation of stock selection as a combinatorial optimization problem
- **OpenJij Integration**: Using OpenJij's simulated annealing and other solvers for optimization
- **Multi-objective Constraints**: 
  - Profitability maximization
  - Correlation minimization
  - Portfolio balance constraints
- **Real-time Data Processing**: Support for live market data integration
- **Backtesting Framework**: Historical performance evaluation
- **Risk Management**: Portfolio risk assessment and management

## Mathematical Formulation

The stock selection problem is formulated as a QUBO problem:

```
minimize: H = H_profit + λ₁H_correlation + λ₂H_balance + λ₃H_constraints
```

Where:
- `H_profit`: Profitability term (negative expected returns)
- `H_correlation`: Correlation penalty term
- `H_balance`: Portfolio balance term
- `H_constraints`: Hard constraints (number of stocks, sector limits, etc.)
- `λ₁, λ₂, λ₃`: Penalty coefficients

## Installation

```bash
pip install -r requirements.txt
```

## Usage

### Basic Stock Selection

```python
from stock_optimizer import StockOptimizer
from data_provider import YahooDataProvider

# Initialize data provider and optimizer
data_provider = YahooDataProvider()
optimizer = StockOptimizer(data_provider)

# Select stocks
selected_stocks = optimizer.select_stocks(
    universe=['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'],
    num_stocks=3,
    lookback_days=252
)

print(f"Selected stocks: {selected_stocks}")
```

### Real-time Trading

```python
from trading_system import RealTimeTradingSystem

# Initialize trading system
trading_system = RealTimeTradingSystem(
    data_provider=data_provider,
    optimizer=optimizer,
    rebalance_frequency='daily'
)

# Start real-time trading
trading_system.start()
```

## Project Structure

```
├── src/
│   ├── stock_optimizer.py      # Main optimization engine
│   ├── qubo_formulation.py     # QUBO problem formulation
│   ├── data_provider.py        # Market data providers
│   ├── trading_system.py       # Real-time trading system
│   ├── risk_manager.py         # Risk management
│   └── utils.py               # Utility functions
├── tests/
│   ├── test_optimizer.py      # Unit tests for optimizer
│   ├── test_qubo.py           # Tests for QUBO formulation
│   └── test_data.py           # Tests for data providers
├── examples/
│   ├── basic_optimization.py  # Basic usage example
│   ├── backtesting.py         # Backtesting example
│   └── real_time_trading.py   # Real-time trading example
├── data/
│   └── sample_data.csv        # Sample market data
└── requirements.txt           # Dependencies
```

## Dependencies

- OpenJij: Quantum-inspired optimization
- NumPy: Numerical computations
- Pandas: Data manipulation
- yfinance: Market data
- scikit-learn: Machine learning utilities
- matplotlib: Visualization

## References

- Tatsumura, K., Hidaka, R., Nakayama, J., Kashimata, T., & Yamasaki, M. (2023). Real-Time Trading System Based on Selections of Potentially Profitable, Uncorrelated, and Balanced Stocks by NP-Hard Combinatorial Optimization. IEEE Access, 11, 120023-120033.
